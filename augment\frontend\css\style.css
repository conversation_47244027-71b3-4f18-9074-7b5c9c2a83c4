/* نظام المحاسبة للرعاة - التنسيقات */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* الإعدادات العامة */
* {
    font-family: 'Noto Sans Arabic', sans-serif;
}

body {
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
}

/* فرض استخدام الأرقام الإنجليزية في جميع أنحاء الموقع */
html {
    font-variant-numeric: lining-nums;
    -webkit-font-feature-settings: "lnum";
    font-feature-settings: "lnum";
}

/* تطبيق الأرقام الإنجليزية على العناصر المحددة */
input[type="number"],
.number,
.currency,
.amount,
.balance,
.total,
.count,
h1, h2, h3, h4, h5, h6,
.card-body h4,
.card-body h3,
.table td,
.table th,
.text-currency,
.badge {
    font-variant-numeric: lining-nums !important;
    -webkit-font-feature-settings: "lnum" !important;
    font-feature-settings: "lnum" !important;
}

/* أنماط بطاقات الرعاة */
.sponsor-cards-container {
    background: white;
}

.cards-page {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    padding: 15px;
    min-height: 90vh;
}

.sponsor-card {
    width: 48%;
    min-height: 500px;
    height: auto;
    margin-bottom: 20px;
    border: 2px solid #28a745;
    border-radius: 8px;
    overflow: visible;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header-custom {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 10px 15px;
    border-bottom: none;
    flex-shrink: 0;
}

.card-header-custom h6 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.card-header-custom small {
    font-size: 12px;
    opacity: 0.9;
}

.card-body-custom {
    padding: 15px;
    flex: 1;
    overflow: visible;
    font-size: 14px;
}

.sponsor-info {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.debts-list h6 {
    font-size: 14px;
    color: #495057;
    margin-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
}

.page-break {
    page-break-before: always;
    height: 0;
    overflow: hidden;
}

/* أنماط الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .sponsor-cards-container {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .cards-page {
        page-break-after: always;
        padding: 10px !important;
        min-height: 95vh !important;
    }
    
    .cards-page:last-child {
        page-break-after: avoid;
    }
    
    .sponsor-card {
        width: 48% !important;
        min-height: 60vh !important;
        height: auto !important;
        margin-bottom: 2vh !important;
        page-break-inside: avoid !important;
        overflow: visible !important;
    }
    
    /* أنماط البطاقات الأسبوعية للطباعة */
    .weekly-cards-page {
        display: grid !important;
        grid-template-columns: 1fr 1fr 1fr !important;
        grid-template-rows: 1fr 1fr !important;
        gap: 10px !important;
        page-break-after: always !important;
        height: 95vh !important;
        margin: 10px !important;
        padding: 0 !important;
    }
    
    .weekly-sponsor-card {
        height: auto !important;
        min-height: 45vh !important;
        overflow: visible !important;
        page-break-inside: avoid !important;
        border: 1px solid #000 !important;
        font-size: 9pt !important;
    }
    
    .weekly-sponsor-card .card-body {
        height: auto !important;
        overflow: visible !important;
        padding: 6px !important;
    }
    
    .weekly-debts-table {
        height: auto !important;
        overflow: visible !important;
    }
    
    .weekly-debts-table table {
        font-size: 8pt !important;
        margin-bottom: 0 !important;
    }
    
    .weekly-debts-table th,
    .weekly-debts-table td {
        padding: 2px !important;
        border: 1px solid #ccc !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #dee2e6 !important;
    }
    
    /* تحسين مظهر الرسومات البيانية عند الطباعة */
    canvas {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
    
    .chart-container {
        page-break-inside: avoid;
        margin-bottom: 20px;
    }
    
    /* إخفاء عناصر التنقل والأزرار */
    .navbar,
    .btn,
    button,
    .modal,
    .dropdown,
    .pagination {
        display: none !important;
    }
    
    /* تحسين النصوص للطباعة */
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000 !important;
        background: white !important;
    }
    
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        page-break-after: avoid;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        border-bottom: 2px solid #dee2e6 !important;
    }
    
    /* أنماط بطاقات الرعاة للطباعة */
    .sponsor-cards-container {
        width: 100% !important;
    }
    
    .cards-page {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        grid-template-rows: 1fr 1fr !important;
        gap: 15px !important;
        height: 95vh !important;
        page-break-after: always !important;
        margin: 10px !important;
        padding: 0 !important;
    }
    
    .sponsor-card {
        border: 2px solid #000 !important;
        padding: 10px !important;
        font-size: 10pt !important;
        overflow: hidden !important;
        box-shadow: none !important;
        background: white !important;
        page-break-inside: avoid !important;
    }
    
    .card-header-custom {
        border-bottom: 1px solid #000 !important;
        margin-bottom: 8px !important;
        padding-bottom: 5px !important;
    }
    
    .sponsor-card table {
        font-size: 9pt !important;
        margin-bottom: 0 !important;
    }
    
    .sponsor-card .table {
        border: none !important;
        margin-bottom: 0 !important;
    }
    
    .sponsor-card .table td,
    .sponsor-card .table th {
        border: 1px solid #ccc !important;
        padding: 3px !important;
    }
    
    .debts-list {
        max-height: none !important;
        overflow: visible !important;
    }
    
    .page-break {
        page-break-before: always !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* أنماط بطاقات الرعاة للشاشة */
.sponsor-cards-container {
    margin: 20px 0;
}

.cards-page {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
    min-height: 600px;
}

.sponsor-card {
    width: 48%;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    page-break-inside: avoid;
}

.sponsor-card .card-header {
    background-color: #007bff;
    color: white;
    padding: 10px;
    text-align: center;
}

.sponsor-card .card-body {
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.sponsor-card .sponsor-info {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.sponsor-card .debts-table {
    font-size: 0.9em;
}

.sponsor-card .debts-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
}

.sponsor-card .debts-table td {
    vertical-align: middle;
    text-align: center;
}

.sponsor-card .debts-table .amount {
    color: #dc3545;
    font-weight: bold;
}

/* أنماط بطاقات الرعاة الأسبوعية */
.weekly-cards-page {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
    page-break-after: always;
}

.weekly-sponsor-card {
    height: auto;
    min-height: 400px;
    overflow: visible;
    page-break-inside: avoid;
}

.weekly-sponsor-card .card-header {
    background-color: #17a2b8;
    color: white;
    padding: 8px;
    text-align: center;
}

.weekly-sponsor-card .card-body {
    padding: 8px;
    height: auto;
    overflow: visible;
}

.weekly-sponsor-card .sponsor-info {
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
    font-size: 0.85em;
}

.weekly-debts-table {
    height: auto;
    overflow: visible;
}

.weekly-debts-table table {
    font-size: 0.75em;
    margin-bottom: 0;
}

.weekly-debts-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
    padding: 4px;
}

.weekly-debts-table td {
    vertical-align: middle;
    text-align: center;
    padding: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.weekly-debts-table .amount {
    color: #dc3545;
    font-weight: bold;
}

/* شريط التنقل */
.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.2rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 0.75rem 0.75rem 0 0 !important;
    font-weight: 600;
}

/* بطاقات الإحصائيات */
.card.bg-primary,
.card.bg-success,
.card.bg-danger,
.card.bg-warning {
    border-radius: 1rem;
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success) 0%, #198754 100%);
}

.card.bg-danger {
    background: linear-gradient(135deg, var(--bs-danger) 0%, #dc3545 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning) 0%, #fd7e14 100%);
}

/* الأزرار */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bb2d3b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
    border: none;
    color: #000;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* التنبيهات */
.alert {
    border-radius: 0.75rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
    color: #0f5132;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #842029;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffecb5 100%);
    color: #664d03;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #b6e3f0 100%);
    color: #055160;
}

/* الأيقونات */
.fas,
.far {
    margin-left: 0.5rem;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section {
    animation: fadeIn 0.5s ease-in-out;
}

/* التحميل */
.loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .table {
        font-size: 0.9rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* تحسينات إضافية */
.text-currency {
    font-weight: 600;
    font-family: 'Courier New', monospace;
    direction: ltr;
    text-align: left;
}

/* فرض استخدام الأرقام الإنجليزية */
.number, .text-currency, .card h4, .card h3, .table td, .table th {
    font-variant-numeric: lining-nums;
    -webkit-font-feature-settings: "lnum";
    font-feature-settings: "lnum";
}

.badge {
    border-radius: 0.5rem;
    font-weight: 500;
}

.modal-content {
    border-radius: 1rem;
    border: none;
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 1rem 1rem 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 1rem 1rem;
}

/* تحسينات التقارير المحسنة */
.report-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

.report-card .card-body {
    padding: 2rem 1.5rem;
}

.report-card i {
    transition: all 0.3s ease;
}

.report-card:hover i {
    transform: scale(1.1);
}

.stat-card {
    padding: 1.5rem;
    border-radius: 1rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    float: right;
    margin-left: 1rem;
    margin-top: -0.5rem;
}

.stat-content {
    position: relative;
    z-index: 1;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 500;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    background: #fff;
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
    height: 400px; /* ارتفاع ثابت */
    min-height: 300px; /* حد أدنى للارتفاع */
    max-height: 500px; /* حد أقصى للارتفاع */
    overflow: hidden; /* منع التمدد خارج الحاوية */
}

.chart-wrapper {
    position: relative;
    width: 100%;
    height: calc(100% - 3rem); /* اطرح ارتفاع العنوان */
    min-height: 250px;
    max-height: 450px;
}

.chart-container canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
}

.chart-container h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    height: 2rem; /* ارتفاع ثابت للعنوان */
    line-height: 2rem;
    flex-shrink: 0; /* منع تقليص العنوان */
}

/* منع تمدد الرسوم البيانية */
.chart-container {
    display: flex;
    flex-direction: column;
}

.chart-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* الإحصائيات المتقدمة */
.advanced-stat {
    text-align: center;
    padding: 1rem;
    background: #fff;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    margin-bottom: 0.5rem;
}

.advanced-stat .stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.advanced-stat .stat-desc {
    display: block;
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

/* الجداول المحسنة */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.sortable {
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
}

.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sortable i {
    margin-left: 0.5rem;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.sortable:hover i {
    opacity: 1;
}

/* مربع البحث */
.search-box {
    position: relative;
}

.search-box input {
    padding-right: 2.5rem;
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* التصفح */
.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: #495057;
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border-color: #0d6efd;
}

/* الشارات المحسنة */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
}

/* تحسينات الطباعة */
@media print {
    .stat-card {
        background: #f8f9fa !important;
        color: #000 !important;
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 10px !important;
        page-break-inside: avoid;
    }

    .chart-container {
        page-break-inside: avoid;
        border: 1px solid #ddd;
    }

    .table {
        font-size: 0.8rem;
    }

    .btn, .search-box, .pagination, .modal {
        display: none !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .stat-icon {
        font-size: 2rem;
        margin-left: 0.75rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .chart-container {
        padding: 0.75rem;
    }

    .advanced-stat {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

/* تأثيرات التحميل المحسنة */
.loading-enhanced {
    text-align: center;
    padding: 3rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    margin: 1rem 0;
}

.loading-enhanced i {
    font-size: 3rem;
    color: #0d6efd;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-enhanced h5 {
    margin-top: 1rem;
    color: #495057;
    font-weight: 600;
}

.loading-enhanced p {
    color: #6c757d;
    margin-bottom: 0;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
    100% { opacity: 1; transform: scale(1); }
}

/* تحسينات التفاعل المتقدم */
.details-row {
    background-color: #f8f9fa;
    border-left: 4px solid #0d6efd;
}

.details-cell {
    padding: 1.5rem !important;
}

.details-content {
    background: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.info-grid {
    display: grid;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.info-value {
    color: #6c757d;
    text-align: left;
    direction: ltr;
}

.financial-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.5rem;
    padding: 1rem;
}

.summary-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.recent-transactions {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 0.5rem;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #fff;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.transaction-item:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-1px);
}

.transaction-item:last-child {
    margin-bottom: 0;
}

.transaction-icon {
    margin-left: 0.75rem;
    font-size: 1.25rem;
}

.transaction-details {
    flex: 1;
    min-width: 0;
}

.transaction-type {
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
}

.transaction-amount {
    font-weight: 700;
    font-size: 1rem;
    margin: 0.25rem 0;
}

.transaction-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.transaction-description {
    font-size: 0.85rem;
    color: #6c757d;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* تمييز نتائج البحث */
mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

/* تحسينات الفلاتر */
.filter-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
    margin-bottom: 0;
}

/* تحسينات الجداول التفاعلية */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.table .btn-group .btn {
    margin: 0 0.125rem;
}

/* تنسيق التقارير الجديدة */
.report-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.report-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e9ecef, #e9ecef);
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.report-card.border-success {
    border-color: #198754 !important;
}

.report-card.border-success::before {
    background: linear-gradient(90deg, #198754, #20c997);
}

.report-card.border-success:hover {
    box-shadow: 0 12px 24px rgba(25, 135, 84, 0.25);
}

.report-card.border-danger {
    border-color: #dc3545 !important;
}

.report-card.border-danger::before {
    background: linear-gradient(90deg, #dc3545, #fd7e14);
}

.report-card.border-danger:hover {
    box-shadow: 0 12px 24px rgba(220, 53, 69, 0.25);
}

.report-card.border-primary {
    border-color: #0d6efd !important;
}

.report-card.border-primary::before {
    background: linear-gradient(90deg, #0d6efd, #6610f2);
}

.report-card.border-primary:hover {
    box-shadow: 0 12px 24px rgba(13, 110, 253, 0.25);
}

/* تحسين بطاقات الإحصائيات */
.stat-card {
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card.bg-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
}

.stat-card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important;
}

.stat-card.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%) !important;
}

.stat-card.bg-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #6f42c1 100%) !important;
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.stat-card.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* تنسيق العملة في الجداول */
.text-currency {
    font-family: 'Segoe UI', 'Roboto', 'Courier New', monospace;
    font-weight: 700;
    direction: ltr;
    text-align: right;
    font-size: 1.1em;
    letter-spacing: 0.5px;
}

/* تحسين الجداول */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: none;
}

.table thead th {
    background: linear-gradient(135deg, #212529 0%, #495057 100%);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
    border: none;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    border-color: rgba(0, 0, 0, 0.05);
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تحسين بطاقات التقارير */
.card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.btn-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
    border: none;
}

.btn-outline-primary {
    border: 2px solid #0d6efd;
    color: #0d6efd;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
    border-color: #0d6efd;
}

.btn-outline-success {
    border: 2px solid #198754;
    color: #198754;
    background: transparent;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
    border-color: #198754;
}

/* تحسين الشارات */
.badge {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* تحسين النماذج */
.modal-content {
    border-radius: 16px;
    border: none;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    border-radius: 16px 16px 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 2px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

/* تحسين حقول الإدخال */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: scale(1.02);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

/* تحسين الأيقونات */
.fas, .far {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* تحسين المسافات */
.mb-4 {
    margin-bottom: 2rem !important;
}

.mb-3 {
    margin-bottom: 1.5rem !important;
}

.p-4 {
    padding: 2rem !important;
}

/* تحسينات إضافية للتقارير */
.sponsor-avatar {
    font-weight: bold;
    text-transform: uppercase;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-row-hover {
    transition: all 0.2s ease;
}

.table-row-hover:hover {
    background-color: rgba(13, 110, 253, 0.08) !important;
    transform: translateX(4px);
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحسين الشارات */
.badge.rounded-pill {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
    font-weight: 600;
}

/* تحسين عناوين البطاقات */
.card-header.bg-gradient {
    position: relative;
    overflow: hidden;
}

.card-header.bg-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08) !important;
}

/* تحسين النصوص */
.fw-bold {
    font-weight: 700 !important;
}

.text-dark {
    color: #212529 !important;
}

/* تحسين الاستجابة للتقارير */
@media (max-width: 992px) {
    .stat-card {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .sponsor-avatar {
        width: 28px !important;
        height: 28px !important;
        font-size: 0.7rem !important;
    }
}

@media (max-width: 768px) {
    .stat-card {
        text-align: center;
        padding: 1.5rem 1rem;
    }

    .stat-icon {
        margin-bottom: 0.75rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .text-currency {
        font-size: 0.9rem;
    }

    .card-header h6 {
        font-size: 0.9rem;
    }
}

/* تحسينات الاستجابة للرسوم البيانية */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
        min-height: 250px;
        max-height: 350px;
        margin-bottom: 1.5rem;
    }

    .chart-wrapper {
        height: calc(100% - 2.5rem);
        min-height: 200px;
    }

    .chart-container h6 {
        font-size: 0.9rem;
        height: 1.8rem;
        line-height: 1.8rem;
    }

    .text-currency {
        font-size: 0.85rem;
    }
}

/* تحسينات الاستجابة للتفاعلات */
@media (max-width: 768px) {
    .details-content {
        padding: 1rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .info-label {
        min-width: auto;
        font-size: 0.9rem;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .transaction-description {
        max-width: none;
        white-space: normal;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }
}

/* تأثيرات التحميل للتفاصيل */
.details-row.loading {
    opacity: 0.7;
}

.details-row.loading .details-content {
    position: relative;
}

.details-row.loading .details-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.details-row.loading .details-content::after {
    content: '\f110';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    color: #0d6efd;
    animation: spin 1s linear infinite;
    z-index: 2;
}

/* تحسينات التنقل في الإدخال المجمع */
.bulk-debt-row:focus-within {
    background-color: rgba(13, 110, 253, 0.1);
    border-radius: 5px;
    padding: 5px;
}

.bulk-debt-sponsor:focus,
.bulk-debt-amount:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* دليل الاختصارات */
.keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.8rem;
    z-index: 1050;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.keyboard-shortcuts.show {
    opacity: 1;
}
