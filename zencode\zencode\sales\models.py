from decimal import Decimal
from django.db import models


class Seller(models.Model):
    name = models.CharField(max_length=150)
    phone = models.CharField(max_length=30, blank=True, null=True)
    is_active = models.<PERSON><PERSON>anField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class WorkDay(models.Model):
    work_date = models.DateField(unique=True)
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.work_date.isoformat()


class DailyEntry(models.Model):
    work_day = models.ForeignKey(WorkDay, on_delete=models.CASCADE)
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE)
    amount_old = models.DecimalField(max_digits=18, decimal_places=4)
    kilos = models.DecimalField(max_digits=18, decimal_places=4)
    note = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Entry {self.id} - {self.seller} - {self.work_day}"


class DayRates(models.Model):
    work_day = models.OneToOneField(WorkDay, on_delete=models.CASCADE)
    usd_to_old = models.DecimalField(max_digits=18, decimal_places=6)
    usd_to_new = models.DecimalField(max_digits=18, decimal_places=6)
    kilo_price = models.DecimalField(max_digits=18, decimal_places=4)
    locked = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Rates {self.work_day}"


class DailyCalculation(models.Model):
    work_day = models.ForeignKey(WorkDay, on_delete=models.CASCADE)
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE)
    amount_old_sum = models.DecimalField(max_digits=18, decimal_places=4)
    kilos_sum = models.DecimalField(max_digits=18, decimal_places=4)
    usd_to_old = models.DecimalField(max_digits=18, decimal_places=6)
    usd_to_new = models.DecimalField(max_digits=18, decimal_places=6)
    kilo_price = models.DecimalField(max_digits=18, decimal_places=4)
    amount_new = models.DecimalField(max_digits=18, decimal_places=4)
    cargo_amount = models.DecimalField(max_digits=18, decimal_places=4)
    total_after_cargo = models.DecimalField(max_digits=18, decimal_places=4)
    approved_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("work_day", "seller")

    def __str__(self):
        return f"Calc {self.work_day} - {self.seller}"


class FinalSale(models.Model):
    work_day = models.ForeignKey(WorkDay, on_delete=models.CASCADE)
    seller = models.ForeignKey(Seller, on_delete=models.CASCADE)
    calc_total = models.DecimalField(max_digits=18, decimal_places=4)
    actual_sale = models.DecimalField(max_digits=18, decimal_places=4)
    profit_loss = models.DecimalField(max_digits=18, decimal_places=4)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("work_day", "seller")

    def save(self, *args, **kwargs):
        # Compute profit/loss automatically
        ct = self.calc_total or Decimal('0')
        asale = self.actual_sale or Decimal('0')
        self.profit_loss = asale - ct
        super().save(*args, **kwargs)

    def __str__(self):
        return f"FinalSale {self.work_day} - {self.seller}"