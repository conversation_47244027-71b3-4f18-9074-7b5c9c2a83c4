# دليل المستخدم - نظام المحاسبة للرعاة

## 📋 نظرة عامة

نظام المحاسبة للرعاة هو تطبيق ويب بسيط وفعال لإدارة الديون والحوالات المالية للرعاة. يوفر النظام واجهة مستخدم باللغة العربية مع إمكانيات شاملة لتتبع المعاملات المالية وإنتاج التقارير.

## 🚀 بدء الاستخدام

### التشغيل السريع:
1. **Windows**: انقر نقراً مزدوجاً على `start_system.bat`
2. **Linux/Mac**: شغل `bash start_system.sh`
3. افتح المتصفح وانتقل إلى: `http://localhost:5000`

### التشغيل اليدوي:
```bash
cd backend
pip install -r requirements.txt
python add_sample_data.py  # (اختياري - لإضافة بيانات تجريبية)
python app.py
```

## 📊 الواجهة الرئيسية

### لوحة التحكم
- **الإحصائيات العامة**: عرض سريع لإجمالي الرعاة والديون والحوالات والأرصدة
- **آخر المعاملات**: قائمة بأحدث الديون والحوالات المسجلة
- **التنقل السريع**: روابط مباشرة لجميع أقسام النظام

## 👥 إدارة الرعاة

### إضافة راعي جديد:
1. انتقل إلى قسم "الرعاة"
2. اضغط على "إضافة راعي جديد"
3. املأ البيانات المطلوبة:
   - **الاسم** (مطلوب)
   - **رقم الهاتف** (اختياري)
   - **العنوان** (اختياري)
4. اضغط "حفظ"

### تعديل بيانات راعي:
1. في قائمة الرعاة، اضغط على أيقونة "تعديل" 📝
2. عدل البيانات المطلوبة
3. اضغط "حفظ"

### حذف راعي:
1. في قائمة الرعاة، اضغط على أيقونة "حذف" 🗑️
2. أكد عملية الحذف
3. **تنبيه**: سيتم حذف جميع الديون والحوالات المرتبطة بالراعي

## 💰 إدارة الديون

### إضافة دين جديد:
1. انتقل إلى قسم "الديون"
2. اضغط على "إضافة دين جديد"
3. املأ البيانات:
   - **الراعي** (مطلوب)
   - **المبلغ** (مطلوب - بالريال اليمني)
   - **التاريخ** (مطلوب)
   - **الوصف** (اختياري)
4. اضغط "حفظ الدين"

### حذف دين:
1. في قائمة الديون، اضغط على أيقونة "حذف" 🗑️
2. أكد عملية الحذف

## 💸 إدارة الحوالات

### إضافة حوالة جديدة:
1. انتقل إلى قسم "الحوالات"
2. اضغط على "إضافة حوالة جديدة"
3. املأ البيانات:
   - **الراعي** (مطلوب)
   - **المبلغ** (مطلوب - بالريال اليمني)
   - **التاريخ** (مطلوب)
   - **مصدر الحوالة** (اختياري - مثل: بنك الكريمي)
4. اضغط "حفظ الحوالة"

### حذف حوالة:
1. في قائمة الحوالات، اضغط على أيقونة "حذف" 🗑️
2. أكد عملية الحذف

## 📈 التقارير

### الملخص الإجمالي:
- عرض إحصائيات شاملة لجميع الرعاة
- إجمالي الديون والحوالات والأرصدة
- تفاصيل كل راعي على حدة

### التقرير اليومي:
1. اختر "التقرير اليومي"
2. حدد التاريخ المطلوب
3. اضغط "عرض التقرير"
4. سيظهر جميع المعاملات (ديون وحوالات) في ذلك التاريخ

### تقرير الراعي:
1. اختر "تقرير الراعي"
2. حدد الراعي من القائمة
3. اضغط "عرض التقرير"
4. سيظهر تفاصيل شاملة للراعي مع جميع معاملاته

### طباعة التقارير:
- جميع التقارير قابلة للطباعة
- اضغط على زر "طباعة" 🖨️ في أي تقرير
- سيفتح نافذة طباعة منسقة

## 💡 نصائح الاستخدام

### أفضل الممارسات:
1. **النسخ الاحتياطي**: انسخ ملف `database/accounting.db` بانتظام
2. **التحقق من البيانات**: راجع المبالغ والتواريخ قبل الحفظ
3. **التقارير الدورية**: اطبع التقارير الشهرية للمراجعة
4. **تنظيم البيانات**: استخدم أوصاف واضحة للديون والحوالات

### اختصارات مفيدة:
- **F5**: تحديث الصفحة
- **Ctrl+P**: طباعة التقرير الحالي
- **Tab**: التنقل بين الحقول في النماذج

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

**الخادم لا يعمل:**
- تأكد من تثبيت Python
- تأكد من تثبيت المكتبات المطلوبة
- تحقق من عدم استخدام المنفذ 5000

**البيانات لا تظهر:**
- تحديث الصفحة (F5)
- تحقق من اتصال الإنترنت
- راجع وحدة التحكم في المتصفح للأخطاء

**خطأ في حفظ البيانات:**
- تأكد من ملء جميع الحقول المطلوبة
- تحقق من صحة تنسيق التاريخ
- تأكد من أن المبالغ أرقام صحيحة

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملف `README.md` للمعلومات التقنية
- تحقق من ملفات السجل في وحدة التحكم
- تأكد من تحديث النظام لآخر إصدار

## 🔄 التحديثات المستقبلية

الميزات المخطط إضافتها:
- نظام المستخدمين والصلاحيات
- التقارير الشهرية والسنوية
- تصدير البيانات إلى Excel
- نظام الإشعارات والتذكيرات
- واجهة الهاتف المحمول

---

**تم تطوير هذا النظام باستخدام Augment Agent**
