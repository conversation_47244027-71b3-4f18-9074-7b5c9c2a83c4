<template>
  <div class="space-y-4">
    <h3 class="text-xl font-semibold">البياعين</h3>
    <form class="flex gap-2 items-end" @submit.prevent="create">
      <div>
        <label class="block text-sm text-gray-600">الاسم</label>
        <input class="border rounded px-2 py-1" v-model="name" required />
      </div>
      <div>
        <label class="block text-sm text-gray-600">الهاتف</label>
        <input class="border rounded px-2 py-1" v-model="phone" />
      </div>
      <button class="bg-blue-600 text-white px-3 py-1 rounded">إضافة</button>
    </form>

    <div class="overflow-x-auto">
      <table class="min-w-full bg-white rounded shadow">
        <thead class="bg-gray-100">
          <tr>
            <th class="text-right p-2">#</th>
            <th class="text-right p-2">الاسم</th>
            <th class="text-right p-2">الهاتف</th>
            <th class="text-right p-2">الحالة</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="s in sellers" :key="s.id" class="border-t">
            <td class="p-2">{{ s.id }}</td>
            <td class="p-2">{{ s.name }}</td>
            <td class="p-2">{{ s.phone || '-' }}</td>
            <td class="p-2">{{ s.is_active ? 'نشط' : 'متوقف' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
import axios from 'axios'
import { ref, onMounted } from 'vue'

const sellers = ref([])
const name = ref('')
const phone = ref('')

async function load(){
  sellers.value = (await axios.get('/api/sellers/')).data
}

async function create(){
  await axios.post('/api/sellers/', { name: name.value, phone: phone.value })
  name.value = ''
  phone.value = ''
  await load()
}

onMounted(load)
</script>