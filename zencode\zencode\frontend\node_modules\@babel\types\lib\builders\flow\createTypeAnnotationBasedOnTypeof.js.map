{"version": 3, "names": ["_index", "require", "_default", "exports", "default", "createTypeAnnotationBasedOnTypeof", "type", "stringTypeAnnotation", "numberTypeAnnotation", "voidTypeAnnotation", "booleanTypeAnnotation", "genericTypeAnnotation", "identifier", "anyTypeAnnotation", "Error"], "sources": ["../../../src/builders/flow/createTypeAnnotationBasedOnTypeof.ts"], "sourcesContent": ["import {\n  anyTypeAnnotation,\n  stringTypeAnnotation,\n  numberTypeAnnotation,\n  voidTypeAnnotation,\n  booleanTypeAnnotation,\n  genericTypeAnnotation,\n  identifier,\n} from \"../generated/index.ts\";\nimport type * as t from \"../../index.ts\";\n\nexport default createTypeAnnotationBasedOnTypeof as {\n  (type: \"string\"): t.StringTypeAnnotation;\n  (type: \"number\"): t.NumberTypeAnnotation;\n  (type: \"undefined\"): t.VoidTypeAnnotation;\n  (type: \"boolean\"): t.<PERSON>TypeAnnotation;\n  (type: \"function\"): t.GenericTypeAnnotation;\n  (type: \"object\"): t.GenericTypeAnnotation;\n  (type: \"symbol\"): t.GenericTypeAnnotation;\n  (type: \"bigint\"): t.AnyTypeAnnotation;\n};\n\n/**\n * Create a type annotation based on typeof expression.\n */\nfunction createTypeAnnotationBasedOnTypeof(type: string): t.FlowType {\n  switch (type) {\n    case \"string\":\n      return stringTypeAnnotation();\n    case \"number\":\n      return numberTypeAnnotation();\n    case \"undefined\":\n      return voidTypeAnnotation();\n    case \"boolean\":\n      return booleanTypeAnnotation();\n    case \"function\":\n      return genericTypeAnnotation(identifier(\"Function\"));\n    case \"object\":\n      return genericTypeAnnotation(identifier(\"Object\"));\n    case \"symbol\":\n      return genericTypeAnnotation(identifier(\"Symbol\"));\n    case \"bigint\":\n      // todo: use BigInt annotation when Flow supports BigInt\n      // https://github.com/facebook/flow/issues/6639\n      return anyTypeAnnotation();\n  }\n  throw new Error(\"Invalid typeof value: \" + type);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAQ+B,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGhBC,iCAAiC;AAchD,SAASA,iCAAiCA,CAACC,IAAY,EAAc;EACnE,QAAQA,IAAI;IACV,KAAK,QAAQ;MACX,OAAO,IAAAC,2BAAoB,EAAC,CAAC;IAC/B,KAAK,QAAQ;MACX,OAAO,IAAAC,2BAAoB,EAAC,CAAC;IAC/B,KAAK,WAAW;MACd,OAAO,IAAAC,yBAAkB,EAAC,CAAC;IAC7B,KAAK,SAAS;MACZ,OAAO,IAAAC,4BAAqB,EAAC,CAAC;IAChC,KAAK,UAAU;MACb,OAAO,IAAAC,4BAAqB,EAAC,IAAAC,iBAAU,EAAC,UAAU,CAAC,CAAC;IACtD,KAAK,QAAQ;MACX,OAAO,IAAAD,4BAAqB,EAAC,IAAAC,iBAAU,EAAC,QAAQ,CAAC,CAAC;IACpD,KAAK,QAAQ;MACX,OAAO,IAAAD,4BAAqB,EAAC,IAAAC,iBAAU,EAAC,QAAQ,CAAC,CAAC;IACpD,KAAK,QAAQ;MAGX,OAAO,IAAAC,wBAAiB,EAAC,CAAC;EAC9B;EACA,MAAM,IAAIC,KAAK,CAAC,wBAAwB,GAAGR,IAAI,CAAC;AAClD", "ignoreList": []}