# Generated by Django 5.0.7 on 2025-08-22 23:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Seller',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=150)),
                ('phone', models.CharField(blank=True, max_length=30, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='WorkDay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('work_date', models.DateField(unique=True)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='DayRates',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('usd_to_old', models.DecimalField(decimal_places=6, max_digits=18)),
                ('usd_to_new', models.DecimalField(decimal_places=6, max_digits=18)),
                ('kilo_price', models.DecimalField(decimal_places=4, max_digits=18)),
                ('locked', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('work_day', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='sales.workday')),
            ],
        ),
        migrations.CreateModel(
            name='DailyEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_old', models.DecimalField(decimal_places=4, max_digits=18)),
                ('kilos', models.DecimalField(decimal_places=4, max_digits=18)),
                ('note', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.seller')),
                ('work_day', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.workday')),
            ],
        ),
        migrations.CreateModel(
            name='FinalSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('calc_total', models.DecimalField(decimal_places=4, max_digits=18)),
                ('actual_sale', models.DecimalField(decimal_places=4, max_digits=18)),
                ('profit_loss', models.DecimalField(decimal_places=4, max_digits=18)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.seller')),
                ('work_day', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.workday')),
            ],
            options={
                'unique_together': {('work_day', 'seller')},
            },
        ),
        migrations.CreateModel(
            name='DailyCalculation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_old_sum', models.DecimalField(decimal_places=4, max_digits=18)),
                ('kilos_sum', models.DecimalField(decimal_places=4, max_digits=18)),
                ('usd_to_old', models.DecimalField(decimal_places=6, max_digits=18)),
                ('usd_to_new', models.DecimalField(decimal_places=6, max_digits=18)),
                ('kilo_price', models.DecimalField(decimal_places=4, max_digits=18)),
                ('amount_new', models.DecimalField(decimal_places=4, max_digits=18)),
                ('cargo_amount', models.DecimalField(decimal_places=4, max_digits=18)),
                ('total_after_cargo', models.DecimalField(decimal_places=4, max_digits=18)),
                ('approved_at', models.DateTimeField(auto_now_add=True)),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.seller')),
                ('work_day', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.workday')),
            ],
            options={
                'unique_together': {('work_day', 'seller')},
            },
        ),
    ]
