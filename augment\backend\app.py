# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لنظام المحاسبة
Main Application for Accounting System
"""

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
from models import db, Sponsor, Debt, Remittance, Branch
from datetime import datetime, date, timedelta
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'accounting-system-secret-key-2024'

# إعداد قاعدة البيانات
basedir = os.path.abspath(os.path.dirname(__file__))
database_path = os.path.join(basedir, '..', 'database', 'accounting.db')
os.makedirs(os.path.dirname(database_path), exist_ok=True)

app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db.init_app(app)

# تمكين CORS للسماح بالطلبات من المتصفح
CORS(app)

# إنشاء الجداول عند بدء التطبيق
with app.app_context():
    db.create_all()

# خدمة الملفات الثابتة (Frontend)
@app.route('/')
def serve_frontend():
    """خدمة الصفحة الرئيسية"""
    frontend_path = os.path.join(basedir, '..', 'frontend')
    return send_from_directory(frontend_path, 'index.html')

@app.route('/<path:filename>')
def serve_static_files(filename):
    """خدمة الملفات الثابتة"""
    frontend_path = os.path.join(basedir, '..', 'frontend')
    return send_from_directory(frontend_path, filename)

# ===== API Routes للفروع =====

@app.route('/api/branches', methods=['GET'])
def get_branches():
    """الحصول على قائمة جميع الفروع"""
    try:
        branches = Branch.query.filter_by(is_active=True).all()
        return jsonify({
            'success': True,
            'data': [branch.to_dict() for branch in branches]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/branches', methods=['POST'])
def create_branch():
    """إنشاء فرع جديد"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name') or not data.get('code'):
            return jsonify({'success': False, 'error': 'اسم الفرع ورمزه مطلوبان'}), 400
        
        # التحقق من عدم تكرار الاسم أو الرمز
        existing_branch = Branch.query.filter(
            (Branch.name == data['name']) | (Branch.code == data['code'])
        ).first()
        
        if existing_branch:
            return jsonify({'success': False, 'error': 'اسم الفرع أو رمزه موجود مسبقاً'}), 400
        
        branch = Branch(
            name=data['name'],
            code=data['code'],
            description=data.get('description', ''),
            address=data.get('address', ''),
            phone=data.get('phone', ''),
            manager_name=data.get('manager_name', '')
        )
        
        db.session.add(branch)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الفرع بنجاح',
            'data': branch.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/branches/<int:branch_id>', methods=['GET'])
def get_branch(branch_id):
    """الحصول على بيانات فرع محدد"""
    try:
        branch = Branch.query.get_or_404(branch_id)
        return jsonify({
            'success': True,
            'data': branch.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/branches/<int:branch_id>', methods=['PUT'])
def update_branch(branch_id):
    """تحديث بيانات فرع"""
    try:
        branch = Branch.query.get_or_404(branch_id)
        data = request.get_json()
        
        if data.get('name'):
            # التحقق من عدم تكرار الاسم
            existing = Branch.query.filter(Branch.name == data['name'], Branch.id != branch_id).first()
            if existing:
                return jsonify({'success': False, 'error': 'اسم الفرع موجود مسبقاً'}), 400
            branch.name = data['name']
            
        if data.get('code'):
            # التحقق من عدم تكرار الرمز
            existing = Branch.query.filter(Branch.code == data['code'], Branch.id != branch_id).first()
            if existing:
                return jsonify({'success': False, 'error': 'رمز الفرع موجود مسبقاً'}), 400
            branch.code = data['code']
            
        if 'description' in data:
            branch.description = data['description']
        if 'address' in data:
            branch.address = data['address']
        if 'phone' in data:
            branch.phone = data['phone']
        if 'manager_name' in data:
            branch.manager_name = data['manager_name']
        if 'is_active' in data:
            branch.is_active = data['is_active']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات الفرع بنجاح',
            'data': branch.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/branches/<int:branch_id>', methods=['DELETE'])
def delete_branch(branch_id):
    """حذف فرع (إلغاء تفعيل)"""
    try:
        branch = Branch.query.get_or_404(branch_id)
        
        # التحقق من وجود ديون مرتبطة بالفرع
        debts_count = Debt.query.filter_by(branch_id=branch_id).count()
        if debts_count > 0:
            # إلغاء تفعيل بدلاً من الحذف
            branch.is_active = False
            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'تم إلغاء تفعيل الفرع بنجاح (يحتوي على {debts_count} دين)'
            })
        else:
            # حذف نهائي إذا لم توجد ديون
            db.session.delete(branch)
            db.session.commit()
            return jsonify({
                'success': True,
                'message': 'تم حذف الفرع بنجاح'
            })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للرعاة =====

@app.route('/api/sponsors', methods=['GET'])
def get_sponsors():
    """الحصول على قائمة جميع الرعاة"""
    try:
        sponsors = Sponsor.query.all()
        return jsonify({
            'success': True,
            'data': [sponsor.to_dict() for sponsor in sponsors]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors', methods=['POST'])
def create_sponsor():
    """إنشاء راعي جديد"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'success': False, 'error': 'اسم الراعي مطلوب'}), 400
        
        sponsor = Sponsor(
            name=data['name'],
            full_name=data.get('full_name', ''),
            phone=data.get('phone', ''),
            address=data.get('address', '')
        )
        
        db.session.add(sponsor)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الراعي بنجاح',
            'data': sponsor.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors/<int:sponsor_id>', methods=['GET'])
def get_sponsor(sponsor_id):
    """الحصول على بيانات راعي محدد"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        return jsonify({
            'success': True,
            'data': sponsor.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors/<int:sponsor_id>', methods=['PUT'])
def update_sponsor(sponsor_id):
    """تحديث بيانات راعي"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        data = request.get_json()
        
        if data.get('name'):
            sponsor.name = data['name']
        if 'full_name' in data:
            sponsor.full_name = data['full_name']
        if 'phone' in data:
            sponsor.phone = data['phone']
        if 'address' in data:
            sponsor.address = data['address']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات الراعي بنجاح',
            'data': sponsor.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors/<int:sponsor_id>', methods=['DELETE'])
def delete_sponsor(sponsor_id):
    """حذف راعي"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        db.session.delete(sponsor)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف الراعي بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للديون =====

@app.route('/api/debts', methods=['GET'])
def get_debts():
    """الحصول على قائمة جميع الديون مع إمكانية الفلترة حسب التاريخ والراعي"""
    try:
        sponsor_id = request.args.get('sponsor_id')
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        
        # بناء الاستعلام
        query = Debt.query
        
        # فلترة حسب الراعي
        if sponsor_id:
            query = query.filter_by(sponsor_id=sponsor_id)
        
        # فلترة حسب تاريخ البداية
        if from_date:
            query = query.filter(Debt.date >= from_date)
        
        # فلترة حسب تاريخ النهاية
        if to_date:
            query = query.filter(Debt.date <= to_date)
        
        # تنفيذ الاستعلام مع الترتيب
        debts = query.order_by(Debt.date.desc()).all()

        return jsonify({
            'success': True,
            'data': [debt.to_dict() for debt in debts]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts', methods=['POST'])
def create_debt():
    """إنشاء دين جديد"""
    try:
        data = request.get_json()

        required_fields = ['sponsor_id', 'branch_id', 'amount', 'date']
        for field in required_fields:
            if not data or not data.get(field):
                return jsonify({'success': False, 'error': f'{field} مطلوب'}), 400

        # التحقق من وجود الراعي
        sponsor = Sponsor.query.get(data['sponsor_id'])
        if not sponsor:
            return jsonify({'success': False, 'error': 'الراعي غير موجود'}), 404
            
        # التحقق من وجود الفرع
        branch = Branch.query.get(data['branch_id'])
        if not branch:
            return jsonify({'success': False, 'error': 'الفرع غير موجود'}), 404
            
        if not branch.is_active:
            return jsonify({'success': False, 'error': 'الفرع غير نشط'}), 400

        debt = Debt(
            sponsor_id=data['sponsor_id'],
            branch_id=data['branch_id'],
            amount=float(data['amount']),
            description=data.get('description', ''),
            date=datetime.strptime(data['date'], '%Y-%m-%d').date()
        )

        db.session.add(debt)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة الدين بنجاح',
            'data': debt.to_dict()
        }), 201

    except ValueError as e:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts/<int:debt_id>', methods=['PUT'])
def update_debt(debt_id):
    """تحديث دين موجود"""
    try:
        debt = Debt.query.get_or_404(debt_id)
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات للتحديث'}), 400
        
        # تحديث البيانات
        if 'sponsor_id' in data:
            sponsor = Sponsor.query.get(data['sponsor_id'])
            if not sponsor:
                return jsonify({'success': False, 'error': 'الراعي المحدد غير موجود'}), 400
            debt.sponsor_id = data['sponsor_id']
        
        if 'branch_id' in data:
            branch = Branch.query.get(data['branch_id'])
            if not branch:
                return jsonify({'success': False, 'error': 'الفرع المحدد غير موجود'}), 400
            debt.branch_id = data['branch_id']
        
        if 'amount' in data:
            amount = float(data['amount'])
            if amount <= 0:
                return jsonify({'success': False, 'error': 'المبلغ يجب أن يكون أكبر من صفر'}), 400
            debt.amount = amount
        
        if 'date' in data:
            debt.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        
        if 'description' in data:
            debt.description = data['description']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث الدين بنجاح',
            'data': debt.to_dict()
        })
        
    except ValueError as e:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts/<int:debt_id>', methods=['DELETE'])
def delete_debt(debt_id):
    """حذف دين"""
    try:
        debt = Debt.query.get_or_404(debt_id)
        db.session.delete(debt)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف الدين بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts/bulk', methods=['POST'])
def create_bulk_debts():
    """إنشاء ديون متعددة (إدخال مجمع)"""
    try:
        data = request.get_json()
        
        if not data or not data.get('debts') or not isinstance(data['debts'], list):
            return jsonify({'success': False, 'error': 'قائمة الديون مطلوبة'}), 400
            
        if len(data['debts']) > 100:  # حد أقصى للأمان
            return jsonify({'success': False, 'error': 'لا يمكن إدخال أكثر من 100 دين في المرة الواحدة'}), 400
        
        # الحصول على القيم المشتركة
        common_branch_id = data.get('branch_id')
        common_date = data.get('date')
        
        # التحقق من الفرع المشترك إذا تم تحديده
        if common_branch_id:
            branch = Branch.query.get(common_branch_id)
            if not branch or not branch.is_active:
                return jsonify({'success': False, 'error': 'الفرع المشترك غير صالح'}), 400
        
        created_debts = []
        errors = []
        
        for i, debt_data in enumerate(data['debts']):
            try:
                # استخدام القيم المشتركة إذا لم تكن محددة في الدين
                sponsor_id = debt_data.get('sponsor_id')
                branch_id = debt_data.get('branch_id', common_branch_id)
                amount = debt_data.get('amount')
                description = debt_data.get('description', '')
                debt_date = debt_data.get('date', common_date)
                
                # التحقق من الحقول المطلوبة
                if not sponsor_id or not branch_id or not amount or not debt_date:
                    errors.append(f'الدين رقم {i+1}: حقول مطلوبة مفقودة')
                    continue
                
                # التحقق من وجود الراعي
                sponsor = Sponsor.query.get(sponsor_id)
                if not sponsor:
                    errors.append(f'الدين رقم {i+1}: الراعي غير موجود')
                    continue
                
                # التحقق من وجود الفرع
                branch = Branch.query.get(branch_id)
                if not branch or not branch.is_active:
                    errors.append(f'الدين رقم {i+1}: الفرع غير صالح')
                    continue
                
                # إنشاء الدين
                debt = Debt(
                    sponsor_id=sponsor_id,
                    branch_id=branch_id,
                    amount=float(amount),
                    description=description,
                    date=datetime.strptime(debt_date, '%Y-%m-%d').date()
                )
                
                db.session.add(debt)
                created_debts.append(debt)
                
            except ValueError as ve:
                errors.append(f'الدين رقم {i+1}: تنسيق التاريخ أو المبلغ غير صحيح')
            except Exception as e:
                errors.append(f'الدين رقم {i+1}: {str(e)}')
        
        # حفظ الديون المنشأة بنجاح
        if created_debts:
            db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'تم إنشاء {len(created_debts)} دين بنجاح',
            'data': {
                'created_count': len(created_debts),
                'error_count': len(errors),
                'created_debts': [debt.to_dict() for debt in created_debts],
                'errors': errors
            }
        }), 201 if created_debts else 400
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts/last-date', methods=['GET'])
def get_last_debt_date():
    """الحصول على آخر تاريخ للديون في قاعدة البيانات"""
    try:
        last_debt = Debt.query.order_by(Debt.date.desc()).first()
        if last_debt:
            return jsonify({
                'success': True,
                'data': {'last_date': last_debt.date.isoformat()}
            })
        else:
            return jsonify({
                'success': True,
                'data': {'last_date': None}
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/sponsor-cards-weekly', methods=['GET'])
def get_weekly_sponsor_cards():
    """تقرير بطاقات الرعاة الأسبوعية - آخر 7 أيام"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        
        # إذا لم يتم تحديد التواريخ، استخدم آخر 7 أيام من قاعدة البيانات
        if not from_date or not to_date:
            last_debt = Debt.query.order_by(Debt.date.desc()).first()
            if last_debt:
                end_date = last_debt.date
                start_date = end_date - timedelta(days=6)
                from_date = start_date.isoformat()
                to_date = end_date.isoformat()
            else:
                return jsonify({
                    'success': False,
                    'error': 'لا توجد ديون في قاعدة البيانات'
                }), 404
        
        # التحقق من عدم تجاوز 7 أيام
        start_date = datetime.fromisoformat(from_date)
        end_date = datetime.fromisoformat(to_date)
        date_diff = (end_date - start_date).days
        
        if date_diff > 6:  # 7 أيام = 6 فروق
            return jsonify({
                'success': False,
                'error': 'لا يمكن اختيار فترة أكثر من 7 أيام'
            }), 400
        
        # جلب الرعاة والديون والحوالات للفترة المحددة
        sponsors = Sponsor.query.all()
        debts = Debt.query.filter(
            Debt.date >= from_date,
            Debt.date <= to_date
        ).order_by(Debt.date.desc()).all()
        
        remittances = Remittance.query.filter(
            Remittance.date >= from_date,
            Remittance.date <= to_date
        ).order_by(Remittance.date.desc()).all()
        
        return jsonify({
            'success': True,
            'data': {
                'sponsors': [sponsor.to_dict() for sponsor in sponsors],
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'from_date': from_date,
                'to_date': to_date
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للحوالات =====

@app.route('/api/remittances', methods=['GET'])
def get_remittances():
    """الحصول على قائمة جميع الحوالات"""
    try:
        sponsor_id = request.args.get('sponsor_id')
        if sponsor_id:
            remittances = Remittance.query.filter_by(sponsor_id=sponsor_id).order_by(Remittance.date.desc()).all()
        else:
            remittances = Remittance.query.order_by(Remittance.date.desc()).all()

        return jsonify({
            'success': True,
            'data': [remittance.to_dict() for remittance in remittances]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/remittances', methods=['POST'])
def create_remittance():
    """إنشاء حوالة جديدة"""
    try:
        data = request.get_json()

        required_fields = ['sponsor_id', 'amount', 'date']
        for field in required_fields:
            if not data or not data.get(field):
                return jsonify({'success': False, 'error': f'{field} مطلوب'}), 400

        # التحقق من وجود الراعي
        sponsor = Sponsor.query.get(data['sponsor_id'])
        if not sponsor:
            return jsonify({'success': False, 'error': 'الراعي غير موجود'}), 404

        remittance = Remittance(
            sponsor_id=data['sponsor_id'],
            amount=float(data['amount']),
            source=data.get('source', ''),
            date=datetime.strptime(data['date'], '%Y-%m-%d').date()
        )

        db.session.add(remittance)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة الحوالة بنجاح',
            'data': remittance.to_dict()
        }), 201

    except ValueError as e:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/remittances/<int:remittance_id>', methods=['DELETE'])
def delete_remittance(remittance_id):
    """حذف حوالة"""
    try:
        remittance = Remittance.query.get_or_404(remittance_id)
        db.session.delete(remittance)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف الحوالة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للتقارير =====

@app.route('/api/reports/summary', methods=['GET'])
def get_summary_report():
    """تقرير الملخص الإجمالي مع إمكانية الفلترة حسب التاريخ والفرع والراعي"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        branch_id = request.args.get('branch_id')
        sponsor_id = request.args.get('sponsor_id')

        # فلترة الرعاة حسب المعايير
        sponsors_query = Sponsor.query
        if sponsor_id:
            sponsors_query = sponsors_query.filter_by(id=sponsor_id)
        sponsors = sponsors_query.all()
        
        branches = Branch.query.filter_by(is_active=True).all()
        
        total_debts = 0
        total_remittances = 0
        total_balance = 0

        sponsors_data = []
        branches_data = []
        
        # تقرير الرعاة
        for sponsor in sponsors:
            # حساب الديون والحوالات مع الفلترة
            debts_query = Debt.query.filter_by(sponsor_id=sponsor.id)
            remittances_query = Remittance.query.filter_by(sponsor_id=sponsor.id)

            # فلترة حسب الفرع
            if branch_id:
                debts_query = debts_query.filter_by(branch_id=branch_id)

            # فلترة حسب التاريخ
            if from_date:
                from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
                debts_query = debts_query.filter(Debt.date >= from_date_obj)
                remittances_query = remittances_query.filter(Remittance.date >= from_date_obj)

            if to_date:
                to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()
                debts_query = debts_query.filter(Debt.date <= to_date_obj)
                remittances_query = remittances_query.filter(Remittance.date <= to_date_obj)

            sponsor_debts = sum(float(debt.amount) for debt in debts_query.all())
            sponsor_remittances = sum(float(remittance.amount) for remittance in remittances_query.all())
            sponsor_balance = sponsor_debts - sponsor_remittances

            # إضافة الراعي فقط إذا كان له معاملات في الفترة/الفرع المحدد أو إذا لم تكن هناك فلاتر
            if sponsor_debts > 0 or sponsor_remittances > 0 or (not from_date and not to_date and not branch_id and not sponsor_id):
                sponsors_data.append({
                    'id': sponsor.id,
                    'name': sponsor.name,
                    'phone': sponsor.phone,
                    'total_debts': sponsor_debts,
                    'total_remittances': sponsor_remittances,
                    'balance': sponsor_balance
                })

            total_debts += sponsor_debts
            total_remittances += sponsor_remittances
            total_balance += sponsor_balance

        # تقرير الفروع
        for branch in branches:
            branch_debts_query = Debt.query.filter_by(branch_id=branch.id)
            
            # فلترة حسب التاريخ
            if from_date:
                from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
                branch_debts_query = branch_debts_query.filter(Debt.date >= from_date_obj)

            if to_date:
                to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()
                branch_debts_query = branch_debts_query.filter(Debt.date <= to_date_obj)

            branch_debts = sum(float(debt.amount) for debt in branch_debts_query.all())
            branch_debts_count = branch_debts_query.count()
            
            branches_data.append({
                'id': branch.id,
                'name': branch.name,
                'code': branch.code,
                'total_debts': branch_debts,
                'debts_count': branch_debts_count
            })

        return jsonify({
            'success': True,
            'data': {
                'sponsors': sponsors_data,
                'branches': branches_data,
                'totals': {
                    'total_debts': total_debts,
                    'total_remittances': total_remittances,
                    'total_balance': total_balance,
                    'sponsors_count': len(sponsors_data),
                    'branches_count': len(branches)
                },
                'filter': {
                    'from_date': from_date,
                    'to_date': to_date,
                    'branch_id': branch_id,
                    'sponsor_id': sponsor_id,
                    'branch_name': Branch.query.get(branch_id).name if branch_id else None,
                    'sponsor_name': Sponsor.query.get(sponsor_id).name if sponsor_id else None
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/sponsor/<int:sponsor_id>', methods=['GET'])
def get_sponsor_report(sponsor_id):
    """تقرير شامل لراعي محدد مع فلترة التاريخ"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        
        # الحصول على معاملات التاريخ
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        
        # بناء استعلام الديون
        debts_query = Debt.query.filter_by(sponsor_id=sponsor_id)
        if from_date:
            debts_query = debts_query.filter(Debt.date >= from_date)
        if to_date:
            debts_query = debts_query.filter(Debt.date <= to_date)
        debts = debts_query.order_by(Debt.date.desc()).all()
        
        # بناء استعلام الحوالات
        remittances_query = Remittance.query.filter_by(sponsor_id=sponsor_id)
        if from_date:
            remittances_query = remittances_query.filter(Remittance.date >= from_date)
        if to_date:
            remittances_query = remittances_query.filter(Remittance.date <= to_date)
        remittances = remittances_query.order_by(Remittance.date.desc()).all()

        return jsonify({
            'success': True,
            'data': {
                'sponsor': sponsor.to_dict(),
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'filters': {
                    'from_date': from_date,
                    'to_date': to_date
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/daily', methods=['GET'])
def get_daily_report():
    """التقرير اليومي"""
    try:
        date_str = request.args.get('date')
        if not date_str:
            report_date = date.today()
        else:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        debts = Debt.query.filter_by(date=report_date).all()
        remittances = Remittance.query.filter_by(date=report_date).all()

        daily_debts_total = sum(float(debt.amount) for debt in debts)
        daily_remittances_total = sum(float(remittance.amount) for remittance in remittances)

        return jsonify({
            'success': True,
            'data': {
                'date': report_date.isoformat(),
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'totals': {
                    'daily_debts': daily_debts_total,
                    'daily_remittances': daily_remittances_total,
                    'net_change': daily_debts_total - daily_remittances_total
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/monthly', methods=['GET'])
def get_monthly_report():
    """التقرير الشهري"""
    try:
        year = int(request.args.get('year', date.today().year))
        month = int(request.args.get('month', date.today().month))

        # الحصول على جميع المعاملات في الشهر المحدد
        debts = db.session.query(Debt).filter(
            db.extract('year', Debt.date) == year,
            db.extract('month', Debt.date) == month
        ).all()

        remittances = db.session.query(Remittance).filter(
            db.extract('year', Remittance.date) == year,
            db.extract('month', Remittance.date) == month
        ).all()

        monthly_debts_total = sum(float(debt.amount) for debt in debts)
        monthly_remittances_total = sum(float(remittance.amount) for remittance in remittances)

        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'month': month,
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'totals': {
                    'monthly_debts': monthly_debts_total,
                    'monthly_remittances': monthly_remittances_total,
                    'net_change': monthly_debts_total - monthly_remittances_total
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'قيم السنة أو الشهر غير صحيحة'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/yearly', methods=['GET'])
def get_yearly_report():
    """التقرير السنوي"""
    try:
        year = int(request.args.get('year', date.today().year))

        # الحصول على جميع المعاملات في السنة المحددة
        debts = db.session.query(Debt).filter(
            db.extract('year', Debt.date) == year
        ).all()

        remittances = db.session.query(Remittance).filter(
            db.extract('year', Remittance.date) == year
        ).all()

        yearly_debts_total = sum(float(debt.amount) for debt in debts)
        yearly_remittances_total = sum(float(remittance.amount) for remittance in remittances)

        # تجميع البيانات حسب الشهر
        monthly_data = {}
        for month in range(1, 13):
            monthly_data[month] = {
                'month': month,
                'debts': 0,
                'remittances': 0,
                'net_change': 0
            }

        # حساب الديون الشهرية
        for debt in debts:
            month = debt.date.month
            monthly_data[month]['debts'] += float(debt.amount)

        # حساب الحوالات الشهرية
        for remittance in remittances:
            month = remittance.date.month
            monthly_data[month]['remittances'] += float(remittance.amount)

        # حساب صافي التغيير لكل شهر
        for month_data in monthly_data.values():
            month_data['net_change'] = month_data['debts'] - month_data['remittances']

        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'monthly_breakdown': list(monthly_data.values()),
                'totals': {
                    'yearly_debts': yearly_debts_total,
                    'yearly_remittances': yearly_remittances_total,
                    'net_change': yearly_debts_total - yearly_remittances_total
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'قيمة السنة غير صحيحة'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/remittances-period', methods=['GET'])
def get_remittances_period_report():
    """تقرير الحوالات لفترة محددة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        if not from_date or not to_date:
            return jsonify({'success': False, 'error': 'يجب تحديد تاريخ البداية والنهاية'}), 400

        from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()

        # الحصول على الحوالات في الفترة المحددة
        remittances = db.session.query(Remittance).filter(
            Remittance.date >= from_date_obj,
            Remittance.date <= to_date_obj
        ).order_by(Remittance.date.desc()).all()

        # تجميع الحوالات حسب الراعي
        sponsors_summary = {}
        sources_summary = {}
        total_amount = 0

        for remittance in remittances:
            sponsor_id = remittance.sponsor_id
            sponsor_name = remittance.sponsor.name
            amount = float(remittance.amount)
            source = remittance.source or 'غير محدد'

            # تجميع حسب الراعي
            if sponsor_id not in sponsors_summary:
                sponsors_summary[sponsor_id] = {
                    'sponsor_id': sponsor_id,
                    'sponsor_name': sponsor_name,
                    'total_amount': 0,
                    'remittances_count': 0,
                    'remittances': []
                }

            sponsors_summary[sponsor_id]['total_amount'] += amount
            sponsors_summary[sponsor_id]['remittances_count'] += 1
            sponsors_summary[sponsor_id]['remittances'].append(remittance.to_dict())

            # تجميع حسب المصدر
            if source not in sources_summary:
                sources_summary[source] = {
                    'source': source,
                    'total_amount': 0,
                    'remittances_count': 0
                }

            sources_summary[source]['total_amount'] += amount
            sources_summary[source]['remittances_count'] += 1

            total_amount += amount

        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'remittances': [remittance.to_dict() for remittance in remittances],
                'sponsors_summary': list(sponsors_summary.values()),
                'sources_summary': list(sources_summary.values()),
                'totals': {
                    'total_amount': total_amount,
                    'total_count': len(remittances),
                    'sponsors_count': len(sponsors_summary),
                    'sources_count': len(sources_summary)
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/debts-period', methods=['GET'])
def get_debts_period_report():
    """تقرير الديون لفترة محددة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        if not from_date or not to_date:
            return jsonify({'success': False, 'error': 'يجب تحديد تاريخ البداية والنهاية'}), 400

        from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()

        # الحصول على الديون في الفترة المحددة
        debts = db.session.query(Debt).filter(
            Debt.date >= from_date_obj,
            Debt.date <= to_date_obj
        ).order_by(Debt.date.desc()).all()

        # تجميع الديون حسب الراعي
        sponsors_summary = {}
        total_amount = 0

        for debt in debts:
            sponsor_id = debt.sponsor_id
            sponsor_name = debt.sponsor.name
            amount = float(debt.amount)

            # تجميع حسب الراعي
            if sponsor_id not in sponsors_summary:
                sponsors_summary[sponsor_id] = {
                    'sponsor_id': sponsor_id,
                    'sponsor_name': sponsor_name,
                    'total_amount': 0,
                    'debts_count': 0,
                    'debts': []
                }

            sponsors_summary[sponsor_id]['total_amount'] += amount
            sponsors_summary[sponsor_id]['debts_count'] += 1
            sponsors_summary[sponsor_id]['debts'].append(debt.to_dict())

            total_amount += amount

        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'debts': [debt.to_dict() for debt in debts],
                'sponsors_summary': list(sponsors_summary.values()),
                'totals': {
                    'total_amount': total_amount,
                    'total_count': len(debts),
                    'sponsors_count': len(sponsors_summary)
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/balance-period', methods=['GET'])
def get_balance_period_report():
    """تقرير الرصيد المتبقي لكل راعي خلال فترة محددة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        if not from_date or not to_date:
            return jsonify({'success': False, 'error': 'يجب تحديد تاريخ البداية والنهاية'}), 400

        from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()

        # الحصول على جميع الرعاة
        sponsors = Sponsor.query.all()
        sponsors_balance = []

        total_debts = 0
        total_remittances = 0
        total_balance = 0

        for sponsor in sponsors:
            # حساب الديون في الفترة المحددة
            period_debts = db.session.query(Debt).filter(
                Debt.sponsor_id == sponsor.id,
                Debt.date >= from_date_obj,
                Debt.date <= to_date_obj
            ).all()

            # حساب الحوالات في الفترة المحددة
            period_remittances = db.session.query(Remittance).filter(
                Remittance.sponsor_id == sponsor.id,
                Remittance.date >= from_date_obj,
                Remittance.date <= to_date_obj
            ).all()

            sponsor_debts = sum(float(debt.amount) for debt in period_debts)
            sponsor_remittances = sum(float(remittance.amount) for remittance in period_remittances)
            sponsor_balance = sponsor_debts - sponsor_remittances

            # إضافة الراعي فقط إذا كان له معاملات في الفترة
            if sponsor_debts > 0 or sponsor_remittances > 0:
                sponsors_balance.append({
                    'sponsor_id': sponsor.id,
                    'sponsor_name': sponsor.full_name or sponsor.name,
                    'sponsor_phone': sponsor.phone,
                    'sponsor_address': sponsor.address,
                    'period_debts': sponsor_debts,
                    'period_remittances': sponsor_remittances,
                    'period_balance': sponsor_balance,
                    'debts_count': len(period_debts),
                    'remittances_count': len(period_remittances),
                    'status': 'مدين' if sponsor_balance > 0 else 'دائن' if sponsor_balance < 0 else 'متوازن'
                })

                total_debts += sponsor_debts
                total_remittances += sponsor_remittances
                total_balance += sponsor_balance

        # ترتيب الرعاة حسب الرصيد المتبقي (الأعلى أولاً)
        sponsors_balance.sort(key=lambda x: x['period_balance'], reverse=True)

        # تصنيف الرعاة
        debtors = [s for s in sponsors_balance if s['period_balance'] > 0]
        creditors = [s for s in sponsors_balance if s['period_balance'] < 0]
        balanced = [s for s in sponsors_balance if s['period_balance'] == 0]

        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'sponsors_balance': sponsors_balance,
                'categories': {
                    'debtors': debtors,
                    'creditors': creditors,
                    'balanced': balanced
                },
                'totals': {
                    'total_debts': total_debts,
                    'total_remittances': total_remittances,
                    'total_balance': total_balance,
                    'sponsors_count': len(sponsors_balance),
                    'debtors_count': len(debtors),
                    'creditors_count': len(creditors),
                    'balanced_count': len(balanced)
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/branch/<int:branch_id>', methods=['GET'])
def get_branch_report(branch_id):
    """تقرير شامل لفرع محدد"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        
        branch = Branch.query.get_or_404(branch_id)
        
        # الحصول على ديون الفرع مع الفلترة
        debts_query = Debt.query.filter_by(branch_id=branch_id)
        
        if from_date:
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
            debts_query = debts_query.filter(Debt.date >= from_date_obj)
            
        if to_date:
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()
            debts_query = debts_query.filter(Debt.date <= to_date_obj)
        
        debts = debts_query.order_by(Debt.date.desc()).all()
        
        # تجميع البيانات حسب الراعي
        sponsors_summary = {}
        total_amount = 0
        
        for debt in debts:
            sponsor_id = debt.sponsor_id
            sponsor_name = debt.sponsor.name
            amount = float(debt.amount)
            
            if sponsor_id not in sponsors_summary:
                sponsors_summary[sponsor_id] = {
                    'sponsor_id': sponsor_id,
                    'sponsor_name': sponsor_name,
                    'sponsor_phone': debt.sponsor.phone,
                    'total_amount': 0,
                    'debts_count': 0,
                    'debts': []
                }
            
            sponsors_summary[sponsor_id]['total_amount'] += amount
            sponsors_summary[sponsor_id]['debts_count'] += 1
            sponsors_summary[sponsor_id]['debts'].append(debt.to_dict())
            
            total_amount += amount

        return jsonify({
            'success': True,
            'data': {
                'branch': branch.to_dict(),
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'debts': [debt.to_dict() for debt in debts],
                'sponsors_summary': list(sponsors_summary.values()),
                'totals': {
                    'total_amount': total_amount,
                    'total_count': len(debts),
                    'sponsors_count': len(sponsors_summary)
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/branches-comparison', methods=['GET'])
def get_branches_comparison_report():
    """تقرير مقارنة بين الفروع"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        
        branches = Branch.query.filter_by(is_active=True).all()
        branches_data = []
        
        for branch in branches:
            # الحصول على ديون الفرع مع الفلترة
            debts_query = Debt.query.filter_by(branch_id=branch.id)
            
            if from_date:
                from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
                debts_query = debts_query.filter(Debt.date >= from_date_obj)
                
            if to_date:
                to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()
                debts_query = debts_query.filter(Debt.date <= to_date_obj)
            
            debts = debts_query.all()
            total_amount = sum(float(debt.amount) for debt in debts)
            
            # عدد الرعاة الفريدين في الفرع
            unique_sponsors = len(set(debt.sponsor_id for debt in debts))
            
            branches_data.append({
                'branch_id': branch.id,
                'branch_name': branch.name,
                'branch_code': branch.code,
                'manager_name': branch.manager_name,
                'total_debts': total_amount,
                'debts_count': len(debts),
                'sponsors_count': unique_sponsors,
                'average_debt': total_amount / len(debts) if debts else 0
            })
        
        # ترتيب الفروع حسب إجمالي الديون
        branches_data.sort(key=lambda x: x['total_debts'], reverse=True)
        
        # حساب الإجماليات
        total_debts = sum(branch['total_debts'] for branch in branches_data)
        total_count = sum(branch['debts_count'] for branch in branches_data)
        
        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'branches': branches_data,
                'totals': {
                    'total_debts': total_debts,
                    'total_count': total_count,
                    'branches_count': len(branches_data),
                    'average_per_branch': total_debts / len(branches_data) if branches_data else 0
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# معالج الأخطاء
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'خطأ في الخادم'}), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام المحاسبة...")
    print("📊 النظام متاح على: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
