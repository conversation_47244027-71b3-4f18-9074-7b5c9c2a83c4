# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لنظام المحاسبة
Database Models for Accounting System
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal

db = SQLAlchemy()

class Branch(db.Model):
    """نموذج الفرع - Branch Model"""
    __tablename__ = 'branches'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='اسم الفرع')
    code = db.Column(db.String(20), nullable=False, unique=True, comment='رمز الفرع')
    description = db.Column(db.Text, comment='وصف الفرع')
    address = db.Column(db.Text, comment='عنوان الفرع')
    phone = db.Column(db.String(20), comment='هاتف الفرع')
    manager_name = db.Column(db.String(100), comment='اسم المدير')
    is_active = db.Column(db.<PERSON>, default=True, comment='حالة الفرع')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    
    # العلاقات - Relationships
    debts = db.relationship('Debt', backref='branch', lazy=True)
    
    def __repr__(self):
        return f'<Branch {self.name}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'address': self.address,
            'phone': self.phone,
            'manager_name': self.manager_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'total_debts': self.get_total_debts(),
            'debts_count': self.get_debts_count()
        }
    
    def get_total_debts(self):
        """حساب إجمالي الديون للفرع"""
        total = db.session.query(db.func.sum(Debt.amount)).filter_by(branch_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def get_debts_count(self):
        """حساب عدد الديون للفرع"""
        return db.session.query(Debt).filter_by(branch_id=self.id).count()

class Sponsor(db.Model):
    """نموذج الراعي - Sponsor Model"""
    __tablename__ = 'sponsors'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='الاسم الأول (للإدخال السريع)')
    full_name = db.Column(db.String(200), comment='الاسم الكامل (للتقارير)')
    phone = db.Column(db.String(20), comment='رقم الهاتف')
    address = db.Column(db.Text, comment='العنوان')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    
    # العلاقات - Relationships
    debts = db.relationship('Debt', backref='sponsor', lazy=True, cascade='all, delete-orphan')
    remittances = db.relationship('Remittance', backref='sponsor', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Sponsor {self.name}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'full_name': self.full_name or self.name,  # استخدام الاسم الأول إذا لم يكن الاسم الكامل متوفراً
            'display_name': self.full_name or self.name,  # للعرض في التقارير
            'phone': self.phone,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'total_debts': self.get_total_debts(),
            'total_remittances': self.get_total_remittances(),
            'balance': self.get_balance()
        }
    
    def get_total_debts(self):
        """حساب إجمالي الديون"""
        total = db.session.query(db.func.sum(Debt.amount)).filter_by(sponsor_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def get_total_remittances(self):
        """حساب إجمالي الحوالات"""
        total = db.session.query(db.func.sum(Remittance.amount)).filter_by(sponsor_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def get_balance(self):
        """حساب الرصيد المتبقي (الديون - الحوالات)"""
        return self.get_total_debts() - self.get_total_remittances()


class Debt(db.Model):
    """نموذج الدين - Debt Model"""
    __tablename__ = 'debts'
    
    id = db.Column(db.Integer, primary_key=True)
    sponsor_id = db.Column(db.Integer, db.ForeignKey('sponsors.id'), nullable=False, comment='معرف الراعي')
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'), nullable=False, comment='معرف الفرع')
    amount = db.Column(db.Numeric(15, 2), nullable=False, comment='المبلغ')
    description = db.Column(db.Text, comment='الوصف')
    date = db.Column(db.Date, nullable=False, comment='التاريخ')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    
    def __repr__(self):
        return f'<Debt {self.amount} for Sponsor {self.sponsor_id}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'sponsor_id': self.sponsor_id,
            'sponsor_name': self.sponsor.name if self.sponsor else None,
            'branch_id': self.branch_id,
            'branch_name': self.branch.name if self.branch else None,
            'branch_code': self.branch.code if self.branch else None,
            'amount': float(self.amount),
            'description': self.description,
            'date': self.date.isoformat() if self.date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class Remittance(db.Model):
    """نموذج الحوالة - Remittance Model"""
    __tablename__ = 'remittances'
    
    id = db.Column(db.Integer, primary_key=True)
    sponsor_id = db.Column(db.Integer, db.ForeignKey('sponsors.id'), nullable=False, comment='معرف الراعي')
    amount = db.Column(db.Numeric(15, 2), nullable=False, comment='المبلغ')
    source = db.Column(db.String(100), comment='مصدر الحوالة')
    date = db.Column(db.Date, nullable=False, comment='التاريخ')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    
    def __repr__(self):
        return f'<Remittance {self.amount} for Sponsor {self.sponsor_id}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'sponsor_id': self.sponsor_id,
            'sponsor_name': self.sponsor.name if self.sponsor else None,
            'amount': float(self.amount),
            'source': self.source,
            'date': self.date.isoformat() if self.date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
