@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    🚀 نظام المحاسبة للرعاة
echo    Sponsors Accounting System
echo ========================================
echo.

echo 📋 التحقق من المتطلبات...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً.
    pause
    exit /b 1
)

echo ✅ Python مثبت
echo.

echo 📦 تثبيت المكتبات المطلوبة...
cd backend
pip install -r requirements.txt > nul 2>&1
if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

echo 🗄️ إعداد قاعدة البيانات...
python add_sample_data.py > nul 2>&1
echo ✅ تم إعداد قاعدة البيانات والبيانات التجريبية
echo.

echo 🌐 بدء تشغيل الخادم...
echo.
echo ========================================
echo    النظام جاهز للاستخدام!
echo    🌐 الرابط: http://localhost:5000
echo    📱 للإيقاف: اضغط Ctrl+C
echo ========================================
echo.

python app.py
