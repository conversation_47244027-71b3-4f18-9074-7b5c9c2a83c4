"""
URL configuration for core project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.views.generic import RedirectView
from rest_framework.routers import DefaultRouter
from sales.views import (
    SellerViewSet, WorkDayViewSet, DailyEntryViewSet,
    DayRatesViewSet, DailyCalculationViewSet, FinalSaleViewSet, ReportsViewSet
)

router = DefaultRouter()
router.register(r'sellers', SellerViewSet)
router.register(r'work-days', WorkDayViewSet)
router.register(r'daily-entries', DailyEntryViewSet)
router.register(r'day-rates', DayRatesViewSet)
router.register(r'calculations', DailyCalculationViewSet, basename='calculations')
router.register(r'final-sales', FinalSaleViewSet)

urlpatterns = [
    path('', RedirectView.as_view(url='/api/', permanent=False)),
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
    path('api/reports/', ReportsViewSet.as_view({'get': 'list'})),
]
