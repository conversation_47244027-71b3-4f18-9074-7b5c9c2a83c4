<template>
  <div>
    <h3>التقارير</h3>
    <div style="display:flex; gap:8px; align-items:center;">
      <label>من</label><input type="date" v-model="from" />
      <label>إلى</label><input type="date" v-model="to" />
      <label>الحالة</label>
      <select v-model="state">
        <option value="all">الكل</option>
        <option value="profit">ربح</option>
        <option value="loss">خسارة</option>
      </select>
      <button @click="load">تحديث</button>
    </div>
    <table v-if="rows.length" border="1" cellspacing="0" cellpadding="6" style="margin-top:12px; width:100%;">
      <thead>
        <tr>
          <th>التاريخ</th>
          <th>البياع</th>
          <th>الإجمالي قبل البيع</th>
          <th>سعر البيع</th>
          <th>الربح/الخسارة</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="r in rows" :key="r.id">
          <td>{{ r.work_day }}</td>
          <td>{{ r.seller_name }}</td>
          <td>{{ fmt(r.calc_total) }}</td>
          <td>{{ fmt(r.actual_sale) }}</td>
          <td :style="{color: Number(r.profit_loss)>=0 ? 'green' : 'red'}">{{ fmt(r.profit_loss) }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<script setup>
import axios from 'axios'
import { ref } from 'vue'

const from = ref('')
const to = ref('')
const state = ref('all')
const rows = ref([])

function fmt(x){
  const n = Number(x||0)
  return n.toLocaleString('ar', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

async function load(){
  const res = await axios.get('/api/reports/', { params: { from: from.value, to: to.value, state: state.value } })
  rows.value = res.data
}
</script>