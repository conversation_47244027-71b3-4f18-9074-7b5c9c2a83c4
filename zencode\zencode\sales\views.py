from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Sum, Q
from .models import Seller, WorkDay, DailyEntry, DayRates, DailyCalculation, FinalSale
from .serializers import (
    SellerSerializer, WorkDaySerializer, DailyEntrySerializer,
    DayRatesSerializer, DailyCalculationSerializer, FinalSaleSerializer,
    DailyEntryBulkSerializer
)
from .services import commit_calculations


class SellerViewSet(viewsets.ModelViewSet):
    queryset = Seller.objects.all().order_by('name')
    serializer_class = SellerSerializer


class WorkDayViewSet(viewsets.ModelViewSet):
    queryset = WorkDay.objects.all().order_by('-work_date')
    serializer_class = WorkDaySerializer


class DailyEntryViewSet(viewsets.ModelViewSet):
    queryset = DailyEntry.objects.all().order_by('-created_at')
    serializer_class = DailyEntrySerializer

    @action(detail=False, methods=['post'])
    def bulk(self, request):
        serializer = DailyEntryBulkSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        entries = serializer.save()
        return Response({'created': len(entries)}, status=status.HTTP_201_CREATED)


class DayRatesViewSet(viewsets.ModelViewSet):
    queryset = DayRates.objects.all().select_related('work_day').order_by('-work_day__work_date')
    serializer_class = DayRatesSerializer

    @action(detail=True, methods=['post'])
    def lock(self, request, pk=None):
        obj = self.get_object()
        obj.locked = True
        obj.save(update_fields=['locked'])
        return Response({'locked': True})


class DailyCalculationViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = DailyCalculationSerializer

    def get_queryset(self):
        qs = DailyCalculation.objects.all().select_related('work_day', 'seller')
        date = self.request.query_params.get('date')
        if date:
            qs = qs.filter(work_day__work_date=date)
        seller_id = self.request.query_params.get('seller_id')
        if seller_id:
            qs = qs.filter(seller_id=seller_id)
        return qs

    @action(detail=False, methods=['post'])
    def commit(self, request):
        date = request.query_params.get('date')
        if not date:
            return Response({'detail': 'date query param is required (YYYY-MM-DD)'}, status=400)
        try:
            wd = WorkDay.objects.get(work_date=date)
        except WorkDay.DoesNotExist:
            return Response({'detail': 'work_day for given date not found'}, status=404)
        if not DayRates.objects.filter(work_day=wd).exists():
            return Response({'detail': 'day rates not found for this date'}, status=400)
        results = commit_calculations(wd)
        return Response(DailyCalculationSerializer(results, many=True).data)


class FinalSaleViewSet(viewsets.ModelViewSet):
    queryset = FinalSale.objects.all().select_related('work_day', 'seller')
    serializer_class = FinalSaleSerializer

    @action(detail=False, methods=['post'])
    def bulk(self, request):
        # Expect: list of {work_day, seller, actual_sale, calc_total}
        items = request.data if isinstance(request.data, list) else []
        created = 0
        for it in items:
            obj, created_flag = FinalSale.objects.update_or_create(
                work_day_id=it['work_day'], seller_id=it['seller'],
                defaults={
                    'calc_total': it['calc_total'],
                    'actual_sale': it['actual_sale'],
                }
            )
            if created_flag:
                created += 1
        return Response({'upserted': len(items), 'created': created})


class ReportsViewSet(viewsets.ViewSet):
    def list(self, request):
        # Filters: from, to, seller_id, state (profit|loss|all), group (daily|monthly|yearly)
        start = request.query_params.get('from')
        end = request.query_params.get('to')
        seller_id = request.query_params.get('seller_id')
        state = request.query_params.get('state', 'all')

        qs = FinalSale.objects.select_related('work_day', 'seller')
        if start:
            qs = qs.filter(work_day__work_date__gte=start)
        if end:
            qs = qs.filter(work_day__work_date__lte=end)
        if seller_id:
            qs = qs.filter(seller_id=seller_id)
        if state == 'profit':
            qs = qs.filter(profit_loss__gt=0)
        elif state == 'loss':
            qs = qs.filter(profit_loss__lt=0)

        data = FinalSaleSerializer(qs, many=True).data
        return Response(data)