// نظام المحاسبة للرعاة - التفاعلات المتقدمة
// Advanced Interactive Features for Accounting System

// متغيرات التفاعل
let expandedRows = new Set();
let tableFilters = {
    search: '',
    sortColumn: null,
    sortDirection: 'asc',
    statusFilter: 'all'
};

// إضافة تفاصيل قابلة للطي للرعاة
function addExpandableRows() {
    const table = document.getElementById('sponsorsTable');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');

    rows.forEach((row, index) => {
        const sponsorId = getSponsorIdFromRow(row);
        if (!sponsorId) return;

        // إضافة زر التوسيع
        const actionsCell = row.querySelector('td:last-child');
        if (actionsCell) {
            const expandBtn = document.createElement('button');
            expandBtn.className = 'btn btn-outline-secondary btn-sm me-1';
            expandBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
            expandBtn.title = 'عرض التفاصيل';
            expandBtn.onclick = () => toggleRowDetails(sponsorId, row);
            
            actionsCell.insertBefore(expandBtn, actionsCell.firstChild);
        }
    });
}

// الحصول على معرف الراعي من الصف
function getSponsorIdFromRow(row) {
    const viewBtn = row.querySelector('button[onclick*="viewSponsorDetails"]');
    if (viewBtn) {
        const onclick = viewBtn.getAttribute('onclick');
        const match = onclick.match(/viewSponsorDetails\((\d+)\)/);
        return match ? parseInt(match[1]) : null;
    }
    return null;
}

// تبديل عرض تفاصيل الصف
async function toggleRowDetails(sponsorId, row) {
    const detailsRowId = `details-${sponsorId}`;
    let detailsRow = document.getElementById(detailsRowId);
    
    if (detailsRow) {
        // إخفاء التفاصيل
        detailsRow.remove();
        expandedRows.delete(sponsorId);
        
        // تغيير أيقونة الزر
        const expandBtn = row.querySelector('button i.fa-chevron-up');
        if (expandBtn) {
            expandBtn.className = 'fas fa-chevron-down';
        }
    } else {
        // عرض التفاصيل
        try {
            const sponsor = currentReportData.sponsors.find(s => s.id === sponsorId);
            if (!sponsor) return;

            // تحميل تفاصيل الراعي
            const response = await fetch(`${API_BASE_URL}/reports/sponsor/${sponsorId}`);
            const data = await response.json();
            
            if (data.success) {
                detailsRow = createDetailsRow(sponsorId, data.data, row.cells.length);
                row.parentNode.insertBefore(detailsRow, row.nextSibling);
                expandedRows.add(sponsorId);
                
                // تغيير أيقونة الزر
                const expandBtn = row.querySelector('button i.fa-chevron-down');
                if (expandBtn) {
                    expandBtn.className = 'fas fa-chevron-up';
                }
                
                // تطبيق تأثير الظهور
                setTimeout(() => {
                    detailsRow.style.opacity = '1';
                    detailsRow.style.transform = 'translateY(0)';
                }, 10);
            }
        } catch (error) {
            console.error('خطأ في تحميل تفاصيل الراعي:', error);
            showError('فشل في تحميل تفاصيل الراعي');
        }
    }
}

// إنشاء صف التفاصيل
function createDetailsRow(sponsorId, sponsorData, colSpan) {
    const detailsRow = document.createElement('tr');
    detailsRow.id = `details-${sponsorId}`;
    detailsRow.className = 'details-row';
    detailsRow.style.opacity = '0';
    detailsRow.style.transform = 'translateY(-10px)';
    detailsRow.style.transition = 'all 0.3s ease';
    
    const detailsCell = document.createElement('td');
    detailsCell.colSpan = colSpan;
    detailsCell.className = 'details-cell';
    
    let html = `
        <div class="details-content">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الراعي
                    </h6>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">${sponsorData.sponsor.phone || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value">${sponsorData.sponsor.address || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ التسجيل:</span>
                            <span class="info-value">${formatDate(sponsorData.sponsor.created_at)}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success mb-3">
                        <i class="fas fa-chart-pie me-2"></i>
                        الملخص المالي
                    </h6>
                    <div class="financial-summary">
                        <div class="summary-item text-danger">
                            <i class="fas fa-minus-circle me-2"></i>
                            <span>إجمالي الديون: ${formatCurrency(sponsorData.sponsor.total_debts)}</span>
                        </div>
                        <div class="summary-item text-success">
                            <i class="fas fa-plus-circle me-2"></i>
                            <span>إجمالي الحوالات: ${formatCurrency(sponsorData.sponsor.total_remittances)}</span>
                        </div>
                        <div class="summary-item ${sponsorData.sponsor.balance >= 0 ? 'text-warning' : 'text-info'}">
                            <i class="fas fa-balance-scale me-2"></i>
                            <span>الرصيد المتبقي: ${formatCurrency(sponsorData.sponsor.balance)}</span>
                        </div>
                    </div>
                </div>
            </div>
    `;
    
    // إضافة آخر المعاملات
    if (sponsorData.debts.length > 0 || sponsorData.remittances.length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-info mb-3">
                        <i class="fas fa-history me-2"></i>
                        آخر المعاملات
                    </h6>
                    <div class="recent-transactions">
        `;
        
        // دمج وترتيب آخر المعاملات
        const allTransactions = [
            ...sponsorData.debts.slice(0, 3).map(debt => ({...debt, type: 'debt'})),
            ...sponsorData.remittances.slice(0, 3).map(remittance => ({...remittance, type: 'remittance'}))
        ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at)).slice(0, 5);
        
        allTransactions.forEach(transaction => {
            const typeIcon = transaction.type === 'debt' ? 
                '<i class="fas fa-minus-circle text-danger"></i>' : 
                '<i class="fas fa-plus-circle text-success"></i>';
            const typeText = transaction.type === 'debt' ? 'دين' : 'حوالة';
            const amountClass = transaction.type === 'debt' ? 'text-danger' : 'text-success';
            
            html += `
                <div class="transaction-item">
                    <div class="transaction-icon">${typeIcon}</div>
                    <div class="transaction-details">
                        <div class="transaction-type">${typeText}</div>
                        <div class="transaction-amount ${amountClass}">${formatCurrency(transaction.amount)}</div>
                        <div class="transaction-date">${formatDate(transaction.date)}</div>
                    </div>
                    <div class="transaction-description">
                        ${transaction.description || transaction.source || '-'}
                    </div>
                </div>
            `;
        });
        
        html += `
                    </div>
                </div>
            </div>
        `;
    }
    
    html += `
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <button class="btn btn-primary btn-sm" onclick="viewSponsorDetails(${sponsorId})">
                        <i class="fas fa-file-alt me-2"></i>
                        عرض التقرير الكامل
                    </button>
                </div>
            </div>
        </div>
    `;
    
    detailsCell.innerHTML = html;
    detailsRow.appendChild(detailsCell);
    
    return detailsRow;
}

// فلترة متقدمة للجداول
function setupAdvancedFiltering() {
    // إضافة فلاتر الحالة
    const filtersContainer = document.querySelector('.card-header');
    if (filtersContainer && !document.getElementById('statusFilter')) {
        const statusFilterHtml = `
            <div class="d-flex align-items-center ms-3">
                <label for="statusFilter" class="form-label me-2 mb-0">الحالة:</label>
                <select class="form-select form-select-sm" id="statusFilter" onchange="applyStatusFilter(this.value)">
                    <option value="all">الكل</option>
                    <option value="debtor">مدين</option>
                    <option value="creditor">دائن</option>
                    <option value="balanced">متوازن</option>
                </select>
            </div>
        `;
        
        const searchBox = filtersContainer.querySelector('.search-box');
        if (searchBox) {
            searchBox.insertAdjacentHTML('afterend', statusFilterHtml);
        }
    }
}

// تطبيق فلتر الحالة
function applyStatusFilter(status) {
    tableFilters.statusFilter = status;
    if (currentReportData && currentReportData.sponsors) {
        renderSponsorsTable(currentReportData.sponsors, 1);
    }
}

// فلترة الرعاة حسب الحالة
function filterSponsorsByStatus(sponsors) {
    if (tableFilters.statusFilter === 'all') return sponsors;
    
    return sponsors.filter(sponsor => {
        switch (tableFilters.statusFilter) {
            case 'debtor':
                return sponsor.balance > 0;
            case 'creditor':
                return sponsor.balance < 0;
            case 'balanced':
                return sponsor.balance === 0;
            default:
                return true;
        }
    });
}

// تحسين البحث مع التمييز
function enhancedSearch(searchTerm, sponsors) {
    if (!searchTerm) return sponsors;
    
    const term = searchTerm.toLowerCase();
    return sponsors.filter(sponsor => {
        return sponsor.name.toLowerCase().includes(term) ||
               (sponsor.phone && sponsor.phone.includes(term)) ||
               (sponsor.address && sponsor.address.toLowerCase().includes(term));
    });
}

// تمييز نتائج البحث
function highlightSearchResults(text, searchTerm) {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// إضافة اختصارات لوحة المفاتيح
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+F للبحث
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-box input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Escape لإغلاق التفاصيل المفتوحة
        if (e.key === 'Escape') {
            expandedRows.forEach(sponsorId => {
                const detailsRow = document.getElementById(`details-${sponsorId}`);
                if (detailsRow) {
                    detailsRow.remove();
                }
            });
            expandedRows.clear();
            
            // إعادة تعيين أيقونات الأزرار
            document.querySelectorAll('.fa-chevron-up').forEach(icon => {
                icon.className = 'fas fa-chevron-down';
            });
        }
    });
}

// تهيئة التفاعلات المتقدمة
function initializeAdvancedInteractions() {
    setupKeyboardShortcuts();
    
    // إضافة التفاعلات عند تحميل التقارير
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const sponsorsTable = document.getElementById('sponsorsTable');
                if (sponsorsTable && !sponsorsTable.dataset.enhanced) {
                    addExpandableRows();
                    setupAdvancedFiltering();
                    sponsorsTable.dataset.enhanced = 'true';
                }
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeAdvancedInteractions();
});
