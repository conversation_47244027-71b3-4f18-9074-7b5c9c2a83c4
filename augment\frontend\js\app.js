// نظام المحاسبة للرعاة - الوظائف الرئيسية

// إعدادات API
const API_BASE_URL = 'http://localhost:5000/api';

// متغيرات عامة
let currentSection = 'dashboard';
let sponsors = [];
let debts = [];
let remittances = [];
let branches = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل نظام المحاسبة للرعاة');
    checkServerConnection();
});

// فحص الاتصال بالخادم
async function checkServerConnection() {
    try {
        const response = await fetch(`${API_BASE_URL}/branches`, {
            method: 'GET',
            timeout: 5000
        });
        
        if (response.ok) {
            console.log('✅ الاتصال بالخادم ناجح');
            loadDashboard();
        } else {
            throw new Error('فشل الاتصال بالخادم');
        }
    } catch (error) {
        console.error('❌ فشل الاتصال بالخادم:', error);
        showServerConnectionError();
    }
}

// عرض خطأ الاتصال بالخادم
function showServerConnectionError() {
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.style.display = 'none');
    
    document.getElementById('dashboard').style.display = 'block';
    document.getElementById('dashboard').innerHTML = `
        <div class="text-center mt-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h4>خطأ في الاتصال بالخادم</h4>
                <p class="mb-3">لا يمكن الاتصال بخادم النظام. يرجى التأكد من:</p>
                <ul class="list-unstyled">
                    <li>• تشغيل الخادم على العنوان: <code>http://localhost:5000</code></li>
                    <li>• عدم وجود برامج تحجب المنفذ 5000</li>
                    <li>• تشغيل سكريبت <code>start_system.bat</code></li>
                </ul>
                <button class="btn btn-primary mt-3" onclick="checkServerConnection()">
                    <i class="fas fa-sync-alt me-2"></i>إعادة المحاولة
                </button>
            </div>
        </div>
    `;
}

// عرض القسم المحدد
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع الروابط
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // عرض القسم المحدد
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.style.display = 'block';
        currentSection = sectionName;
    }
    
    // إضافة الفئة النشطة للرابط المحدد
    const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // تحميل بيانات القسم
    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'sponsors':
            loadSponsors();
            break;
        case 'debts':
            loadDebts();
            break;
        case 'remittances':
            loadRemittances();
            break;
        case 'branches':
            loadBranches();
            break;
        case 'reports':
            loadReports();
            break;
    }
}

// تحميل لوحة التحكم
async function loadDashboard() {
    try {
        showLoading('recent-transactions');
        
        // تحميل الملخص الإجمالي
        const summaryResponse = await fetch(`${API_BASE_URL}/reports/summary`);
        const summaryData = await summaryResponse.json();
        
        if (summaryData.success) {
            updateDashboardCards(summaryData.data.totals);
            displayRecentTransactions();
        } else {
            showError('فشل في تحميل بيانات الملخص');
        }
    } catch (error) {
        console.error('خطأ في تحميل لوحة التحكم:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحديث بطاقات الإحصائيات
function updateDashboardCards(totals) {
    document.getElementById('total-sponsors').textContent = totals.sponsors_count || 0;
    document.getElementById('total-debts').textContent = formatCurrency(totals.total_debts || 0);
    document.getElementById('total-remittances').textContent = formatCurrency(totals.total_remittances || 0);
    document.getElementById('total-balance').textContent = formatCurrency(totals.total_balance || 0);
}

// عرض آخر المعاملات
async function displayRecentTransactions() {
    try {
        // الحصول على آخر الديون والحوالات
        const [debtsResponse, remittancesResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/debts`),
            fetch(`${API_BASE_URL}/remittances`)
        ]);
        
        const debtsData = await debtsResponse.json();
        const remittancesData = await remittancesResponse.json();
        
        if (debtsData.success && remittancesData.success) {
            const recentDebts = debtsData.data.slice(0, 5);
            const recentRemittances = remittancesData.data.slice(0, 5);
            
            // دمج وترتيب المعاملات حسب التاريخ
            const allTransactions = [
                ...recentDebts.map(debt => ({...debt, type: 'debt'})),
                ...recentRemittances.map(remittance => ({...remittance, type: 'remittance'}))
            ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at)).slice(0, 10);
            
            displayTransactionsList(allTransactions);
        }
    } catch (error) {
        console.error('خطأ في تحميل المعاملات الأخيرة:', error);
        document.getElementById('recent-transactions').innerHTML = 
            '<p class="text-danger text-center">خطأ في تحميل المعاملات</p>';
    }
}

// عرض قائمة المعاملات
function displayTransactionsList(transactions) {
    const container = document.getElementById('recent-transactions');
    
    if (transactions.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد معاملات حديثة</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead class="table-secondary"><tr><th>النوع</th><th>الراعي</th><th>المبلغ</th><th>التاريخ</th><th>الوصف</th></tr></thead><tbody>';
    
    transactions.forEach(transaction => {
        const typeIcon = transaction.type === 'debt' ? 
            '<i class="fas fa-minus-circle text-danger"></i> دين' : 
            '<i class="fas fa-plus-circle text-success"></i> حوالة';
        
        const amountClass = transaction.type === 'debt' ? 'text-danger' : 'text-success';
        const amountSign = transaction.type === 'debt' ? '-' : '+';
        
        html += `
            <tr>
                <td>${typeIcon}</td>
                <td>${transaction.sponsor_name || 'غير محدد'}</td>
                <td class="${amountClass} text-currency">${amountSign}${formatCurrency(transaction.amount)}</td>
                <td>${formatDate(transaction.date)}</td>
                <td>${transaction.description || transaction.source || '-'}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// تحميل الرعاة
async function loadSponsors() {
    try {
        showLoading('sponsors');

        const response = await fetch(`${API_BASE_URL}/sponsors`);
        const data = await response.json();

        if (data.success) {
            displaySponsorsPage(data.data);
        } else {
            showError('فشل في تحميل بيانات الرعاة');
        }
    } catch (error) {
        console.error('خطأ في تحميل الرعاة:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الرعاة
function displaySponsorsPage(sponsorsData) {
    sponsors = sponsorsData;

    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users me-2"></i>إدارة الرعاة</h2>
            <button class="btn btn-primary" onclick="showAddSponsorModal()">
                <i class="fas fa-plus me-2"></i>إضافة راعي جديد
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الرعاة (${sponsors.length})
                </h5>
            </div>
            <div class="card-body">
    `;

    if (sponsors.length === 0) {
        html += '<p class="text-muted text-center">لا توجد رعاة مسجلين</p>';
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-secondary">
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>إجمالي الديون</th>
                            <th>إجمالي الحوالات</th>
                            <th>الرصيد المتبقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        sponsors.forEach(sponsor => {
            const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';

            html += `
                <tr>
                    <td><strong>${sponsor.display_name || sponsor.full_name || sponsor.name}</strong></td>
                    <td>${sponsor.phone || '-'}</td>
                    <td>${sponsor.address || '-'}</td>
                    <td class="text-danger text-currency">${formatCurrency(sponsor.total_debts)}</td>
                    <td class="text-success text-currency">${formatCurrency(sponsor.total_remittances)}</td>
                    <td class="${balanceClass} text-currency"><strong>${formatCurrency(sponsor.balance)}</strong></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewSponsorDetails(${sponsor.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editSponsor(${sponsor.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSponsor(${sponsor.id}, '${sponsor.display_name || sponsor.full_name || sponsor.name}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    html += `
            </div>
        </div>

        <!-- Modal إضافة/تعديل راعي -->
        <div class="modal fade" id="sponsorModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sponsorModalTitle">إضافة راعي جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="sponsorForm">
                            <input type="hidden" id="sponsorId">
                            <div class="mb-3">
                                <label for="sponsorName" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="sponsorName" required>
                                <div class="form-text">الاسم المستخدم في الإدخال السريع</div>
                            </div>
                            <div class="mb-3">
                                <label for="sponsorFullName" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="sponsorFullName">
                                <div class="form-text">الاسم الكامل المعروض في التقارير</div>
                            </div>
                            <div class="mb-3">
                                <label for="sponsorPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="sponsorPhone">
                            </div>
                            <div class="mb-3">
                                <label for="sponsorAddress" class="form-label">العنوان</label>
                                <textarea class="form-control" id="sponsorAddress" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveSponsor()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('sponsors').innerHTML = html;
}

// تحميل الديون
async function loadDebts() {
    try {
        showLoading('debts');

        const [debtsResponse, sponsorsResponse, branchesResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/debts`),
            fetch(`${API_BASE_URL}/sponsors`),
            fetch(`${API_BASE_URL}/branches`)
        ]);

        const debtsData = await debtsResponse.json();
        const sponsorsData = await sponsorsResponse.json();
        const branchesData = await branchesResponse.json();

        if (debtsData.success && sponsorsData.success && branchesData.success) {
            debts = debtsData.data;
            sponsors = sponsorsData.data;
            branches = branchesData.data;
            displayDebtsPage();
        } else {
            showError('فشل في تحميل بيانات الديون');
        }
    } catch (error) {
        console.error('خطأ في تحميل الديون:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الديون
function displayDebtsPage() {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-minus-circle me-2"></i>إدارة الديون</h2>
            <div class="btn-group">
                <button class="btn btn-danger" onclick="showAddDebtModal()">
                    <i class="fas fa-plus me-2"></i>إضافة دين جديد
                </button>
                <button class="btn btn-warning" onclick="showBulkDebtModal()">
                    <i class="fas fa-layer-group me-2"></i>إدخال مجمع
                </button>
            </div>
        </div>

        <!-- مربع البحث والفلاتر -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="debtSearchInput" class="form-label">البحث</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="debtSearchInput" 
                               placeholder="ابحث في أسماء الرعاة أو الوصف..." 
                               onkeypress="if(event.key==='Enter') searchDebts()">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="debtFromDate" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="debtFromDate">
                    </div>
                    <div class="col-md-3">
                        <label for="debtToDate" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="debtToDate">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" onclick="searchDebts()">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearDebtFilters()">
                                <i class="fas fa-times me-1"></i>مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الديون (<span id="debtsCount">${debts.length}</span>)
                </h5>
            </div>
            <div class="card-body" id="debtsTableContainer">
    `;

    // سيتم ملء الجدول بواسطة دالة renderDebtsTable
    html += `
            </div>
        </div>

        <!-- Modal إضافة دين -->
        <div class="modal fade" id="debtModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="debtModalTitle">إضافة دين جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="debtForm">
                            <div class="mb-3">
                                <label for="debtSponsor" class="form-label">الراعي *</label>
                                <select class="form-select" id="debtSponsor" required>
                                    <option value="">اختر الراعي</option>
    `;

    sponsors.forEach(sponsor => {
        html += `<option value="${sponsor.id}">${sponsor.display_name || sponsor.full_name || sponsor.name}</option>`;
    });

    html += `
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="debtBranch" class="form-label">الفرع *</label>
                                <select class="form-select" id="debtBranch" required>
                                    <option value="">اختر الفرع</option>
    `;

    branches.filter(branch => branch.is_active).forEach(branch => {
        html += `<option value="${branch.id}">${branch.name} (${branch.code})</option>`;
    });

    html += `
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="debtAmount" class="form-label">المبلغ (ريال) *</label>
                                <input type="number" class="form-control" id="debtAmount" min="0" step="1" required>
                            </div>
                            <div class="mb-3">
                                <label for="debtDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="debtDate" required>
                            </div>
                            <div class="mb-3">
                                <label for="debtDescription" class="form-label">الوصف</label>
                                <textarea class="form-control" id="debtDescription" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" id="saveDebtBtn" onclick="saveDebt()">حفظ الدين</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal الإدخال المجمع للديون -->
        <div class="modal fade" id="bulkDebtModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الإدخال المجمع للديون</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="bulkDebtForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="bulkDebtBranch" class="form-label">الفرع *</label>
                                    <select class="form-select" id="bulkDebtBranch" required>
                                        <option value="">اختر الفرع</option>
    `;

    branches.filter(branch => branch.is_active).forEach(branch => {
        html += `<option value="${branch.id}">${branch.name} (${branch.code})</option>`;
    });

    html += `
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="bulkDebtDate" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="bulkDebtDate" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="bulkDebtDescription" class="form-label">الوصف المشترك</label>
                                <input type="text" class="form-control" id="bulkDebtDescription" 
                                       placeholder="مثال: ديون شهر يناير 2024">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الديون</label>
                                <div id="bulkDebtsContainer">
                                    <div class="bulk-debt-row row mb-2">
                                        <div class="col-md-6">
                                            <input type="text" class="form-control bulk-debt-sponsor" 
                                                   placeholder="اكتب اسم الراعي..." required
                                                   data-sponsor-id=""
                                                   oninput="handleSponsorAutocomplete(this)"
                                                   onkeydown="handleBulkDebtNavigation(event, this)"
                                                   onblur="validateSponsorInput(this)">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="number" class="form-control bulk-debt-amount" 
                                                   placeholder="المبلغ (بالآلاف)" min="0" step="1" required
                                                   onkeydown="handleBulkDebtNavigation(event, this)">
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-outline-danger btn-sm" 
                                                    onclick="removeBulkDebtRow(this)" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addBulkDebtRowWithFocus()">
                                    <i class="fas fa-plus me-2"></i>إضافة دين آخر
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-warning" onclick="saveBulkDebts()">
                            <i class="fas fa-save me-2"></i>حفظ جميع الديون
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('debts').innerHTML = html;

    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('debtDate').value = new Date().toISOString().split('T')[0];
    
    // عرض جدول الديون
    renderDebtsTable(debts);
}

// عرض جدول الديون مع إمكانية الفلترة
function renderDebtsTable(debtsToShow) {
    const container = document.getElementById('debtsTableContainer');
    
    if (debtsToShow.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد ديون مطابقة للبحث</p>';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead class="table-secondary">
                    <tr>
                        <th>التاريخ</th>
                        <th>الراعي</th>
                        <th>الفرع</th>
                        <th>المبلغ</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    debtsToShow.forEach(debt => {
        html += `
            <tr>
                <td>${formatDate(debt.date)}</td>
                <td><strong>${debt.sponsor_name || 'غير محدد'}</strong></td>
                <td><span class="badge bg-info">${debt.branch_name || 'غير محدد'}</span></td>
                <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                <td>${debt.description || '-'}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm me-1" onclick="editDebt(${debt.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteDebt(${debt.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// البحث في الديون (يعمل بدون تاريخ افتراضياً)
function searchDebts() {
    const searchTerm = document.getElementById('debtSearchInput').value.toLowerCase();
    const fromDate = document.getElementById('debtFromDate').value;
    const toDate = document.getElementById('debtToDate').value;

    let filteredDebts = debts.filter(debt => {
        // فلترة النص (أساسية)
        const matchesSearch = !searchTerm || 
            (debt.sponsor_name && debt.sponsor_name.toLowerCase().includes(searchTerm)) ||
            (debt.description && debt.description.toLowerCase().includes(searchTerm)) ||
            (debt.branch_name && debt.branch_name.toLowerCase().includes(searchTerm));

        // فلترة التاريخ (اختيارية)
        let matchesDate = true;
        if (fromDate || toDate) {
            const debtDate = new Date(debt.date);
            const matchesFromDate = !fromDate || debtDate >= new Date(fromDate);
            const matchesToDate = !toDate || debtDate <= new Date(toDate);
            matchesDate = matchesFromDate && matchesToDate;
        }

        return matchesSearch && matchesDate;
    });

    // تحديث عدد النتائج
    document.getElementById('debtsCount').textContent = filteredDebts.length;
    
    // عرض النتائج المفلترة
    renderDebtsTable(filteredDebts);
}

// للتوافق مع الكود القديم
function filterDebts() {
    searchDebts();
}

// مسح الفلاتر
function clearDebtFilters() {
    document.getElementById('debtSearchInput').value = '';
    document.getElementById('debtFromDate').value = '';
    document.getElementById('debtToDate').value = '';
    
    // إعادة عرض جميع الديون
    document.getElementById('debtsCount').textContent = debts.length;
    renderDebtsTable(debts);
}

// تحميل الحوالات
async function loadRemittances() {
    try {
        showLoading('remittances');

        const [remittancesResponse, sponsorsResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/remittances`),
            fetch(`${API_BASE_URL}/sponsors`)
        ]);

        const remittancesData = await remittancesResponse.json();
        const sponsorsData = await sponsorsResponse.json();

        if (remittancesData.success && sponsorsData.success) {
            remittances = remittancesData.data;
            sponsors = sponsorsData.data;
            displayRemittancesPage();
        } else {
            showError('فشل في تحميل بيانات الحوالات');
        }
    } catch (error) {
        console.error('خطأ في تحميل الحوالات:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الحوالات
function displayRemittancesPage() {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-plus-circle me-2"></i>إدارة الحوالات</h2>
            <button class="btn btn-success" onclick="showAddRemittanceModal()">
                <i class="fas fa-plus me-2"></i>إضافة حوالة جديدة
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الحوالات (${remittances.length})
                </h5>
            </div>
            <div class="card-body">
    `;

    if (remittances.length === 0) {
        html += '<p class="text-muted text-center">لا توجد حوالات مسجلة</p>';
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-secondary">
                        <tr>
                            <th>التاريخ</th>
                            <th>الراعي</th>
                            <th>المبلغ</th>
                            <th>مصدر الحوالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${formatDate(remittance.date)}</td>
                    <td><strong>${remittance.sponsor_name || 'غير محدد'}</strong></td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                    <td>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteRemittance(${remittance.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    html += `
            </div>
        </div>

        <!-- Modal إضافة حوالة -->
        <div class="modal fade" id="remittanceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة حوالة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="remittanceForm">
                            <div class="mb-3">
                                <label for="remittanceSponsor" class="form-label">الراعي *</label>
                                <select class="form-select" id="remittanceSponsor" required>
                                    <option value="">اختر الراعي</option>
    `;

    sponsors.forEach(sponsor => {
        html += `<option value="${sponsor.id}">${sponsor.display_name || sponsor.full_name || sponsor.name}</option>`;
    });

    html += `
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="remittanceAmount" class="form-label">المبلغ (ريال) *</label>
                                <input type="number" class="form-control" id="remittanceAmount" min="0" step="1" required>
                            </div>
                            <div class="mb-3">
                                <label for="remittanceDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="remittanceDate" required>
                            </div>
                            <div class="mb-3">
                                <label for="remittanceSource" class="form-label">مصدر الحوالة</label>
                                <input type="text" class="form-control" id="remittanceSource" placeholder="مثال: بنك الكريمي، حوالة نقدية">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveRemittance()">حفظ الحوالة</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('remittances').innerHTML = html;

    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('remittanceDate').value = new Date().toISOString().split('T')[0];
}

// تحميل الفروع
async function loadBranches() {
    try {
        showLoading('branches');

        const response = await fetch(`${API_BASE_URL}/branches`);
        const data = await response.json();

        if (data.success) {
            branches = data.data;
            displayBranchesPage();
        } else {
            showError('فشل في تحميل بيانات الفروع');
        }
    } catch (error) {
        console.error('خطأ في تحميل الفروع:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الفروع
function displayBranchesPage() {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-building me-2"></i>إدارة الفروع</h2>
            <button class="btn btn-primary" onclick="showAddBranchModal()">
                <i class="fas fa-plus me-2"></i>إضافة فرع جديد
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الفروع (${branches.length})
                </h5>
            </div>
            <div class="card-body">
    `;

    if (branches.length === 0) {
        html += '<p class="text-muted text-center">لا توجد فروع مسجلة</p>';
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-secondary">
                        <tr>
                            <th>اسم الفرع</th>
                            <th>رمز الفرع</th>
                            <th>عدد الديون</th>
                            <th>إجمالي الديون</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        branches.forEach(branch => {
            const statusBadge = branch.is_active ? 
                '<span class="badge bg-success">نشط</span>' : 
                '<span class="badge bg-secondary">غير نشط</span>';

            html += `
                <tr>
                    <td><strong>${branch.name}</strong></td>
                    <td><code>${branch.code}</code></td>
                    <td>${branch.manager_name || '-'}</td>
                    <td>${branch.phone || '-'}</td>
                    <td class="text-center">${branch.debts_count || 0}</td>
                    <td class="text-danger text-currency">${formatCurrency(branch.total_debts || 0)}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="viewBranchDetails(${branch.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editBranch(${branch.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-${branch.is_active ? 'secondary' : 'success'}" 
                                    onclick="toggleBranchStatus(${branch.id}, ${branch.is_active})" 
                                    title="${branch.is_active ? 'تعطيل' : 'تفعيل'}">
                                <i class="fas fa-${branch.is_active ? 'pause' : 'play'}"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    html += `
            </div>
        </div>

        <!-- Modal إضافة/تعديل فرع -->
        <div class="modal fade" id="branchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="branchModalTitle">إضافة فرع جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="branchForm">
                            <input type="hidden" id="branchId">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="branchName" class="form-label">اسم الفرع *</label>
                                        <input type="text" class="form-control" id="branchName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="branchCode" class="form-label">رمز الفرع *</label>
                                        <input type="text" class="form-control" id="branchCode" required 
                                               placeholder="مثال: BR001">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="branchDescription" class="form-label">وصف الفرع</label>
                                <textarea class="form-control" id="branchDescription" rows="2"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="branchManagerName" class="form-label">اسم المدير</label>
                                        <input type="text" class="form-control" id="branchManagerName">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="branchPhone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="branchPhone">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="branchAddress" class="form-label">العنوان</label>
                                <textarea class="form-control" id="branchAddress" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="branchIsActive" checked>
                                    <label class="form-check-label" for="branchIsActive">
                                        فرع نشط
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveBranch()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تفاصيل الفرع -->
        <div class="modal fade" id="branchDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="branchDetailsTitle">تفاصيل الفرع</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="branchDetailsContent">
                            <p class="text-center">جاري تحميل البيانات...</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('branches').innerHTML = html;
}

// تحميل التقارير
async function loadReports() {
    try {
        showLoading('reports');
        displayReportsPage();
    } catch (error) {
        console.error('خطأ في تحميل التقارير:', error);
        showError('خطأ في تحميل التقارير');
    }
}

// عرض صفحة التقارير
function displayReportsPage() {
    let html = `
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير
                </h2>
            </div>
        </div>

        <!-- أزرار التقارير المحسنة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie fa-3x text-primary mb-3"></i>
                        <h5>الملخص الإجمالي المحسن</h5>
                        <p class="text-muted">ملخص شامل مع رسوم بيانية وإحصائيات متقدمة</p>
                        <button class="btn btn-primary" onclick="loadSummaryReport()">
                            <i class="fas fa-chart-line me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-day fa-3x text-success mb-3"></i>
                        <h5>التقرير اليومي</h5>
                        <p class="text-muted">معاملات تاريخ محدد مع تحليل مفصل</p>
                        <button class="btn btn-success" onclick="showDailyReportModal()">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>
                        <h5>التقرير الشهري</h5>
                        <p class="text-muted">تحليل شامل للمعاملات الشهرية مع الاتجاهات</p>
                        <button class="btn btn-info" onclick="showMonthlyReportModal()">
                            <i class="fas fa-chart-bar me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x text-warning mb-3"></i>
                        <h5>تقرير الراعي</h5>
                        <p class="text-muted">تفاصيل شاملة لراعي محدد مع التاريخ</p>
                        <button class="btn btn-warning" onclick="showSponsorReportModal()">
                            <i class="fas fa-user-chart me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- التقارير الجديدة للفترات الزمنية -->
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-calendar-range me-2"></i>
                    تقارير الفترات الزمنية
                </h4>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 report-card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-transfer fa-3x text-success mb-3"></i>
                        <h5>تقرير الحوالات للفترة</h5>
                        <p class="text-muted">جميع الحوالات المستلمة خلال فترة محددة مع التجميع حسب الراعي والمصدر</p>
                        <button class="btn btn-success" onclick="showRemittancesPeriodModal()">
                            <i class="fas fa-calendar-plus me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 report-card border-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice-dollar fa-3x text-danger mb-3"></i>
                        <h5>تقرير الديون للفترة</h5>
                        <p class="text-muted">جميع الديون المسجلة خلال فترة محددة مع التجميع حسب الراعي</p>
                        <button class="btn btn-danger" onclick="showDebtsPeriodModal()">
                            <i class="fas fa-calendar-minus me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 report-card border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-balance-scale fa-3x text-primary mb-3"></i>
                        <h5>تقرير الأرصدة للفترة</h5>
                        <p class="text-muted">الرصيد المتبقي لكل راعي خلال فترة محددة مع التصنيف</p>
                        <button class="btn btn-primary" onclick="showBalancePeriodModal()">
                            <i class="fas fa-calculator me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- تقرير بطاقات الرعاة -->
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-success mb-3">
                    <i class="fas fa-id-card me-2"></i>
                    تقارير البطاقات
                </h4>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-6 col-md-8 mx-auto mb-3">
                <div class="card h-100 report-card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-id-card-alt fa-3x text-success mb-3"></i>
                        <h5>بطاقات الرعاة للفترة</h5>
                        <p class="text-muted">طباعة بطاقات مفصلة للرعاة مع ديونهم خلال فترة محددة (4 بطاقات في الصفحة)</p>
                        <button class="btn btn-success" onclick="showSponsorCardsModal()">
                            <i class="fas fa-print me-2"></i>إنشاء البطاقات
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-8 mx-auto mb-3">
                <div class="card h-100 report-card border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-week fa-3x text-info mb-3"></i>
                        <h5>بطاقات الرعاة الأسبوعية</h5>
                        <p class="text-muted">بطاقات ثابتة لآخر 7 أيام من البيانات (6 بطاقات في الصفحة)</p>
                        <button class="btn btn-info" onclick="showWeeklySponsorCardsModal()">
                            <i class="fas fa-print me-2"></i>إنشاء البطاقات الأسبوعية
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة عرض التقارير -->
        <div id="report-content">
            <div class="card">
                <div class="card-body text-center text-muted">
                    <i class="fas fa-chart-bar fa-4x mb-3"></i>
                    <h5>اختر نوع التقرير</h5>
                    <p>استخدم الأزرار أعلاه لعرض التقارير المختلفة</p>
                </div>
            </div>
        </div>

        <!-- Modal التقرير اليومي -->
        <div class="modal fade" id="dailyReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">التقرير اليومي</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="reportDate" class="form-label">اختر التاريخ</label>
                            <input type="date" class="form-control" id="reportDate">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="loadDailyReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal التقرير الشهري -->
        <div class="modal fade" id="monthlyReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">التقرير الشهري</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="monthlyYear" class="form-label">السنة</label>
                                <select class="form-select" id="monthlyYear">
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="monthlyMonth" class="form-label">الشهر</label>
                                <select class="form-select" id="monthlyMonth">
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-info" onclick="loadEnhancedMonthlyReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الراعي -->
        <div class="modal fade" id="sponsorReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الراعي</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="reportSponsor" class="form-label">اختر الراعي</label>
                            <select class="form-select" id="reportSponsor" onchange="toggleDateFilters()">
                                <option value="">جاري تحميل الرعاة...</option>
                            </select>
                        </div>
                        
                        <!-- فلاتر التاريخ -->
                        <div id="dateFiltersContainer" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                اتركها فارغة لعرض جميع المعاملات
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="reportFromDate" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="reportFromDate">
                                </div>
                                <div class="col-md-6">
                                    <label for="reportToDate" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="reportToDate">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-warning" onclick="loadSponsorReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الحوالات للفترة -->
        <div class="modal fade" id="remittancesPeriodModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الحوالات للفترة المحددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="remittancesFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="remittancesFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="remittancesToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="remittancesToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم عرض جميع الحوالات المستلمة خلال الفترة المحددة مع التجميع حسب الراعي ومصدر الحوالة
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="loadRemittancesPeriodReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الديون للفترة -->
        <div class="modal fade" id="debtsPeriodModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الديون للفترة المحددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="debtsFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="debtsFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="debtsToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="debtsToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم عرض جميع الديون المسجلة خلال الفترة المحددة مع التجميع حسب الراعي
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="loadDebtsPeriodReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الأرصدة للفترة -->
        <div class="modal fade" id="balancePeriodModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الأرصدة للفترة المحددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="balanceFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="balanceFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="balanceToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="balanceToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم حساب الرصيد المتبقي لكل راعي خلال الفترة المحددة وتصنيف الرعاة (مدين/دائن/متوازن)
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="loadBalancePeriodReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير بطاقات الرعاة -->
        <div class="modal fade" id="sponsorCardsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">بطاقات الرعاة للفترة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="cardsFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="cardsFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cardsToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="cardsToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم إنشاء بطاقات مفصلة لجميع الرعاة مع ديونهم خلال الفترة المحددة (4 بطاقات في كل صفحة)
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="generateSponsorCards()">
                            <i class="fas fa-print me-2"></i>إنشاء البطاقات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير بطاقات الرعاة الأسبوعية -->
        <div class="modal fade" id="weeklySponsorCardsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">بطاقات الرعاة الأسبوعية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="weeklyCardsFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="weeklyCardsFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="weeklyCardsToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="weeklyCardsToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                بطاقات ثابتة الحجم لآخر 7 أيام من البيانات (6 بطاقات في كل صفحة). 
                                لا يمكن اختيار فترة أكثر من 7 أيام.
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-info" onclick="generateWeeklySponsorCards()">
                            <i class="fas fa-print me-2"></i>إنشاء البطاقات الأسبوعية
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('reports').innerHTML = html;

    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];

    // تحميل قائمة الرعاة للتقرير
    loadSponsorsForReport();
}

// وظائف مساعدة

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount) + ' ريال';
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-YE', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        numberingSystem: 'latn'  // استخدام الأرقام اللاتينية
    });
}

// عرض رسالة التحميل
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري التحميل...</p>
            </div>
        `;
    }
}

// إخفاء رسالة التحميل
function hideLoading() {
    // هذه الدالة تستخدم لإخفاء رسالة التحميل
    // في التطبيقات الأخرى، لكن هنا نستخدم showLoading مع المحتوى الجديد
}

// عرض رسالة التحميل المحسنة مع شريط التقدم
function showEnhancedLoading(containerId, message = 'جاري تحميل التقرير المحسن') {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="loading-enhanced">
                <i class="fas fa-chart-line fa-spin"></i>
                <h5>${message}</h5>
                <p>يرجى الانتظار بينما نقوم بإعداد البيانات والرسوم البيانية...</p>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%" id="loadingProgress"></div>
                </div>
            </div>
        `;

        // محاكاة شريط التقدم
        simulateProgress();
    }
}

// محاكاة تقدم التحميل
function simulateProgress() {
    const progressBar = document.getElementById('loadingProgress');
    if (!progressBar) return;

    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;

        progressBar.style.width = progress + '%';

        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 200);

    // إكمال التقدم عند انتهاء التحميل
    setTimeout(() => {
        if (progressBar) {
            progressBar.style.width = '100%';
            setTimeout(() => {
                clearInterval(interval);
            }, 300);
        }
    }, 2000);
}

// عرض رسالة خطأ
function showError(message) {
    console.error('خطأ:', message);
    
    // إنشاء تنبيه مرئي
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>خطأ:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثواني
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// عرض رسالة نجاح
function showSuccess(message) {
    console.log('نجح:', message);
    
    // إنشاء تنبيه نجاح مرئي
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        <strong>نجح:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثواني
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// ===== وظائف إدارة الرعاة =====

// عرض نموذج إضافة راعي جديد
function showAddSponsorModal() {
    document.getElementById('sponsorModalTitle').textContent = 'إضافة راعي جديد';
    document.getElementById('sponsorForm').reset();
    document.getElementById('sponsorId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('sponsorModal'));
    modal.show();
}

// تعديل راعي
function editSponsor(sponsorId) {
    const sponsor = sponsors.find(s => s.id === sponsorId);
    if (!sponsor) return;

    document.getElementById('sponsorModalTitle').textContent = 'تعديل بيانات الراعي';
    document.getElementById('sponsorId').value = sponsor.id;
    document.getElementById('sponsorName').value = sponsor.name;
    document.getElementById('sponsorFullName').value = sponsor.full_name || '';
    document.getElementById('sponsorPhone').value = sponsor.phone || '';
    document.getElementById('sponsorAddress').value = sponsor.address || '';

    const modal = new bootstrap.Modal(document.getElementById('sponsorModal'));
    modal.show();
}

// حفظ راعي (إضافة أو تعديل)
async function saveSponsor() {
    const sponsorId = document.getElementById('sponsorId').value;
    const name = document.getElementById('sponsorName').value.trim();
    const full_name = document.getElementById('sponsorFullName').value.trim();
    const phone = document.getElementById('sponsorPhone').value.trim();
    const address = document.getElementById('sponsorAddress').value.trim();

    if (!name) {
        alert('الاسم الأول مطلوب');
        return;
    }

    const sponsorData = { name, full_name, phone, address };

    try {
        let response;
        if (sponsorId) {
            // تعديل راعي موجود
            response = await fetch(`${API_BASE_URL}/sponsors/${sponsorId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(sponsorData)
            });
        } else {
            // إضافة راعي جديد
            response = await fetch(`${API_BASE_URL}/sponsors`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(sponsorData)
            });
        }

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('sponsorModal')).hide();
            loadSponsors(); // إعادة تحميل قائمة الرعاة
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الراعي:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// حذف راعي
async function deleteSponsor(sponsorId, sponsorName) {
    if (!confirm(`هل أنت متأكد من حذف الراعي "${sponsorName}"؟\nسيتم حذف جميع الديون والحوالات المرتبطة به.`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/sponsors/${sponsorId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            loadSponsors(); // إعادة تحميل قائمة الرعاة
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حذف الراعي:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// عرض تفاصيل راعي
function viewSponsorDetails(sponsorId) {
    const sponsor = sponsors.find(s => s.id === sponsorId);
    if (!sponsor) return;

    // الانتقال إلى صفحة التقارير مع عرض تقرير الراعي
    showSection('reports');
    // سيتم تطوير هذه الوظيفة لاحقاً
}

// ===== وظائف إدارة الديون =====

// متغير لتتبع حالة التعديل
let isEditingDebt = false;
let currentEditDebtId = null;

// عرض نموذج إضافة دين جديد
function showAddDebtModal() {
    isEditingDebt = false;
    currentEditDebtId = null;
    
    document.getElementById('debtModalTitle').textContent = 'إضافة دين جديد';
    document.getElementById('saveDebtBtn').textContent = 'حفظ الدين';
    document.getElementById('debtForm').reset();
    document.getElementById('debtDate').value = new Date().toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('debtModal'));
    modal.show();
}

// عرض نموذج تعديل دين
function editDebt(debtId) {
    const debt = debts.find(d => d.id === debtId);
    if (!debt) {
        showError('لم يتم العثور على الدين');
        return;
    }

    isEditingDebt = true;
    currentEditDebtId = debtId;
    
    document.getElementById('debtModalTitle').textContent = 'تعديل الدين';
    document.getElementById('saveDebtBtn').textContent = 'حفظ التعديل';
    
    // تعبئة النموذج بالبيانات الحالية
    document.getElementById('debtSponsor').value = debt.sponsor_id;
    document.getElementById('debtBranch').value = debt.branch_id;
    document.getElementById('debtAmount').value = debt.amount;
    document.getElementById('debtDate').value = debt.date;
    document.getElementById('debtDescription').value = debt.description || '';

    const modal = new bootstrap.Modal(document.getElementById('debtModal'));
    modal.show();
}

// حفظ دين جديد
async function saveDebt() {
    const sponsorId = document.getElementById('debtSponsor').value;
    const branchId = document.getElementById('debtBranch').value;
    const amount = document.getElementById('debtAmount').value;
    const date = document.getElementById('debtDate').value;
    const description = document.getElementById('debtDescription').value.trim();

    if (!sponsorId || !branchId || !amount || !date) {
        alert('جميع الحقول المطلوبة يجب ملؤها');
        return;
    }

    if (parseFloat(amount) <= 0) {
        alert('المبلغ يجب أن يكون أكبر من صفر');
        return;
    }

    const debtData = {
        sponsor_id: parseInt(sponsorId),
        branch_id: parseInt(branchId),
        amount: parseFloat(amount),
        date: date,
        description: description
    };

    try {
        let response;
        
        if (isEditingDebt && currentEditDebtId) {
            // تحديث دين موجود
            response = await fetch(`${API_BASE_URL}/debts/${currentEditDebtId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(debtData)
            });
        } else {
            // إضافة دين جديد
            response = await fetch(`${API_BASE_URL}/debts`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(debtData)
            });
        }

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('debtModal')).hide();
            loadDebts(); // إعادة تحميل قائمة الديون
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الدين:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// حذف دين
async function deleteDebt(debtId) {
    if (!confirm('هل أنت متأكد من حذف هذا الدين؟')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/debts/${debtId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            loadDebts(); // إعادة تحميل قائمة الديون
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حذف الدين:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// ===== وظائف إدارة الحوالات =====

// عرض نموذج إضافة حوالة جديدة
function showAddRemittanceModal() {
    document.getElementById('remittanceForm').reset();
    document.getElementById('remittanceDate').value = new Date().toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('remittanceModal'));
    modal.show();
}

// حفظ حوالة جديدة
async function saveRemittance() {
    const sponsorId = document.getElementById('remittanceSponsor').value;
    const amount = document.getElementById('remittanceAmount').value;
    const date = document.getElementById('remittanceDate').value;
    const source = document.getElementById('remittanceSource').value.trim();

    if (!sponsorId || !amount || !date) {
        alert('جميع الحقول المطلوبة يجب ملؤها');
        return;
    }

    if (parseFloat(amount) <= 0) {
        alert('المبلغ يجب أن يكون أكبر من صفر');
        return;
    }

    const remittanceData = {
        sponsor_id: parseInt(sponsorId),
        amount: parseFloat(amount),
        date: date,
        source: source
    };

    try {
        const response = await fetch(`${API_BASE_URL}/remittances`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(remittanceData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('remittanceModal')).hide();
            loadRemittances(); // إعادة تحميل قائمة الحوالات
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الحوالة:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// حذف حوالة
async function deleteRemittance(remittanceId) {
    if (!confirm('هل أنت متأكد من حذف هذه الحوالة؟')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/remittances/${remittanceId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            loadRemittances(); // إعادة تحميل قائمة الحوالات
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حذف الحوالة:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// ===== وظائف التقارير =====

// عرض نافذة بطاقات الرعاة الأسبوعية
function showWeeklySponsorCardsModal() {
    const modal = new bootstrap.Modal(document.getElementById('weeklySponsorCardsModal'));
    
    // جلب آخر 7 أيام من قاعدة البيانات وتعيينها كافتراضي
    fetch(`${API_BASE_URL}/debts/last-date`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.last_date) {
                const lastDate = new Date(data.data.last_date);
                const firstDate = new Date(lastDate);
                firstDate.setDate(lastDate.getDate() - 6);
                
                document.getElementById('weeklyCardsFromDate').value = firstDate.toISOString().split('T')[0];
                document.getElementById('weeklyCardsToDate').value = lastDate.toISOString().split('T')[0];
            }
        })
        .catch(error => {
            console.error('Error fetching last debt date:', error);
            // في حالة الخطأ، استخدم آخر 7 أيام من اليوم الحالي
            const today = new Date();
            const weekAgo = new Date(today);
            weekAgo.setDate(today.getDate() - 6);
            
            document.getElementById('weeklyCardsFromDate').value = weekAgo.toISOString().split('T')[0];
            document.getElementById('weeklyCardsToDate').value = today.toISOString().split('T')[0];
        });
    
    modal.show();
}

// إنشاء بطاقات الرعاة الأسبوعية
async function generateWeeklySponsorCards() {
    const fromDate = document.getElementById('weeklyCardsFromDate').value;
    const toDate = document.getElementById('weeklyCardsToDate').value;
    
    if (!fromDate || !toDate) {
        alert('يرجى تحديد التواريخ');
        return;
    }
    
    // التحقق من عدم تجاوز 7 أيام
    const startDate = new Date(fromDate);
    const endDate = new Date(toDate);
    const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    
    if (diffDays > 6) {
        alert('لا يمكن اختيار فترة أكثر من 7 أيام');
        return;
    }
    
    try {
        showLoading('جاري تحميل البيانات...');
        
        console.log('Fetching weekly cards data...', fromDate, toDate);
        const response = await fetch(`${API_BASE_URL}/reports/sponsor-cards-weekly?from_date=${fromDate}&to_date=${toDate}`);
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        hideLoading();
        
        if (!data.success) {
            alert(data.error || 'حدث خطأ في تحميل البيانات');
            return;
        }
        
        // إغلاق النافذة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('weeklySponsorCardsModal'));
        modal.hide();
        
        // عرض البطاقات
        displayWeeklySponsorCards(data.data.sponsors, data.data.debts, data.data.from_date, data.data.to_date, data.data.remittances);
        
    } catch (error) {
        hideLoading();
        console.error('Error generating weekly sponsor cards:', error);
        alert(`حدث خطأ في تحميل البيانات: ${error.message}`);
    }
}

// عرض بطاقات الرعاة الأسبوعية
function displayWeeklySponsorCards(sponsors, debts, fromDate, toDate, remittances = []) {
    // إنشاء مصفوفة التواريخ من البداية إلى النهاية
    const last7Days = [];
    const currentDate = new Date(fromDate);
    const endDate = new Date(toDate);
    
    // إضافة جميع التواريخ من البداية إلى النهاية (شاملة)
    while (currentDate <= endDate) {
        last7Days.push(currentDate.toISOString().split('T')[0]);
        currentDate.setDate(currentDate.getDate() + 1);
    }
    
    console.log('Generated dates:', last7Days);
    
    // تجميع الديون حسب الراعي والتاريخ
    const debtsBySponsors = {};
    debts.forEach(debt => {
        if (!debtsBySponsors[debt.sponsor_id]) {
            debtsBySponsors[debt.sponsor_id] = {};
        }
        if (!debtsBySponsors[debt.sponsor_id][debt.date]) {
            debtsBySponsors[debt.sponsor_id][debt.date] = [];
        }
        debtsBySponsors[debt.sponsor_id][debt.date].push(debt);
    });

    // تجميع الحوالات حسب الراعي والتاريخ
    const remittancesBySponsors = {};
    remittances.forEach(remittance => {
        if (!remittancesBySponsors[remittance.sponsor_id]) {
            remittancesBySponsors[remittance.sponsor_id] = {};
        }
        if (!remittancesBySponsors[remittance.sponsor_id][remittance.date]) {
            remittancesBySponsors[remittance.sponsor_id][remittance.date] = [];
        }
        remittancesBySponsors[remittance.sponsor_id][remittance.date].push(remittance);
    });
    
    // فلترة الرعاة الذين لديهم ديون في الفترة المحددة فقط
    const sponsorsWithDebts = sponsors.filter(sponsor => {
        const sponsorDebts = debtsBySponsors[sponsor.id];
        if (!sponsorDebts) return false;
        
        // التحقق من وجود ديون في التواريخ المحددة
        return last7Days.some(date => sponsorDebts[date] && sponsorDebts[date].length > 0);
    });
    
    if (sponsorsWithDebts.length === 0) {
        document.getElementById('report-content').innerHTML = `
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <h5>لا توجد بيانات</h5>
                <p>لا توجد ديون للرعاة في الفترة المحددة من ${fromDate} إلى ${toDate}</p>
            </div>
        `;
        return;
    }
    
    let cardsHtml = '';
    let cardCount = 0;
    
    sponsorsWithDebts.forEach(sponsor => {
        if (cardCount % 6 === 0) {
            if (cardCount > 0) cardsHtml += '</div>';
            cardsHtml += '<div class="weekly-cards-page">';
        }
        
        const sponsorDebts = debtsBySponsors[sponsor.id] || {};
        const sponsorRemittances = remittancesBySponsors[sponsor.id] || {};
        let totalAmount = 0;
        let totalRemittances = 0;
        
        // حساب إجمالي الديون
        Object.values(sponsorDebts).forEach(dayDebts => {
            dayDebts.forEach(debt => totalAmount += debt.amount);
        });
        
        // حساب إجمالي الحوالات
        Object.values(sponsorRemittances).forEach(dayRemittances => {
            dayRemittances.forEach(remittance => totalRemittances += remittance.amount);
        });
        
        // الصافي = الديون - الحوالات
        const netAmount = totalAmount - totalRemittances;
        
        cardsHtml += `
            <div class="weekly-sponsor-card">
                <div class="card h-100">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0">${sponsor.full_name || sponsor.name}</h6>
                        <small>${fromDate} - ${toDate}</small>
                    </div>
                    <div class="card-body p-2">
                        <div class="sponsor-info mb-2">
                            <small><strong>الهاتف:</strong> ${sponsor.phone || 'غير محدد'}</small><br>
                            <small><strong>إجمالي الديون:</strong> <span class="text-danger">${totalAmount.toLocaleString()}</span></small><br>
                            ${totalRemittances > 0 ? `<small><strong>إجمالي الحوالات:</strong> <span class="text-success">${totalRemittances.toLocaleString()}</span></small><br>` : ''}
                            <small><strong>الصافي:</strong> <span class="text-primary">${netAmount.toLocaleString()}</span></small>
                        </div>
                        <div class="weekly-debts-table">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr class="table-light">
                                        <th style="width: 25%">التاريخ</th>
                                        <th style="width: 20%">المبلغ</th>
                                        <th style="width: 55%">الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>`;
        
        // إنشاء صفوف الجدول لكل يوم من الفترة المحددة
        last7Days.forEach(date => {
            const dayDebts = sponsorDebts[date] || [];
            const dayRemittances = sponsorRemittances[date] || [];
            
            let rowContent = '';
            let totalDayAmount = 0;
            let totalDayRemittances = 0;
            
            // حساب إجمالي الديون لهذا اليوم
            if (dayDebts.length > 0) {
                totalDayAmount = dayDebts.reduce((sum, debt) => sum + debt.amount, 0);
            }
            
            // حساب إجمالي الحوالات لهذا اليوم
            if (dayRemittances.length > 0) {
                totalDayRemittances = dayRemittances.reduce((sum, remittance) => sum + remittance.amount, 0);
            }
            
            // الصافي لهذا اليوم
            const netDayAmount = totalDayAmount - totalDayRemittances;
            
            // إنشاء الوصف
            let descriptions = [];
            if (dayDebts.length > 0) {
                const debtDescriptions = dayDebts.map(debt => 
                    debt.description ? `${debt.description} (${debt.amount.toLocaleString()})` : debt.amount.toLocaleString()
                );
                descriptions.push(`ديون: ${debtDescriptions.join(' + ')}`);
            }
            
            if (dayRemittances.length > 0) {
                const remittanceDescriptions = dayRemittances.map(remittance => 
                    remittance.description ? `${remittance.description} (${remittance.amount.toLocaleString()})` : remittance.amount.toLocaleString()
                );
                descriptions.push(`حوالات: ${remittanceDescriptions.join(' + ')}`);
            }
            
            if (totalDayAmount > 0 || totalDayRemittances > 0) {
                const amountClass = netDayAmount > 0 ? 'text-danger' : netDayAmount < 0 ? 'text-success' : 'text-muted';
                cardsHtml += `
                    <tr>
                        <td class="text-center">${new Date(date).toLocaleDateString('en-GB')}</td>
                        <td class="${amountClass} text-center">${netDayAmount.toLocaleString()}</td>
                        <td style="font-size: 0.8em">${descriptions.join(' | ')}</td>
                    </tr>
                `;
            } else {
                cardsHtml += `
                    <tr>
                        <td class="text-center">${new Date(date).toLocaleDateString('en-GB')}</td>
                        <td class="text-muted text-center">-</td>
                        <td class="text-muted" style="font-size: 0.8em">لا توجد معاملات</td>
                    </tr>
                `;
            }
        });
        
        cardsHtml += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        cardCount++;
    });
    
    if (cardCount > 0) cardsHtml += '</div>';
    
    const html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>بطاقات الرعاة الأسبوعية (${fromDate} - ${toDate})</h4>
        </div>
        <div id="weekly-cards-container">
            ${cardsHtml}
        </div>
    `;
    
    document.getElementById('report-content').innerHTML = html;
}


// تحميل قائمة الرعاة للتقارير
async function loadSponsorsForReport() {
    try {
        const response = await fetch(`${API_BASE_URL}/sponsors`);
        const data = await response.json();

        if (data.success) {
            const select = document.getElementById('reportSponsor');
            select.innerHTML = '<option value="">اختر الراعي</option>';

            data.data.forEach(sponsor => {
                select.innerHTML += `<option value="${sponsor.id}">${sponsor.name}</option>`;
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل الرعاة:', error);
    }
}

// عرض نموذج التقرير اليومي
function showDailyReportModal() {
    document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];
    const modal = new bootstrap.Modal(document.getElementById('dailyReportModal'));
    modal.show();
}

// عرض نموذج التقرير الشهري
function showMonthlyReportModal() {
    // تعبئة قائمة السنوات
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    let yearOptions = '';
    for (let year = currentYear - 5; year <= currentYear + 1; year++) {
        yearOptions += `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}</option>`;
    }
    document.getElementById('monthlyYear').innerHTML = yearOptions;

    // تحديد الشهر الحالي
    document.getElementById('monthlyMonth').value = currentMonth;

    const modal = new bootstrap.Modal(document.getElementById('monthlyReportModal'));
    modal.show();
}

// إظهار/إخفاء فلاتر التاريخ
function toggleDateFilters() {
    const sponsorSelect = document.getElementById('reportSponsor');
    const dateFiltersContainer = document.getElementById('dateFiltersContainer');
    
    if (sponsorSelect.value) {
        dateFiltersContainer.style.display = 'block';
    } else {
        dateFiltersContainer.style.display = 'none';
        // مسح قيم التاريخ
        document.getElementById('reportFromDate').value = '';
        document.getElementById('reportToDate').value = '';
    }
}

// عرض نموذج تقرير الراعي
function showSponsorReportModal() {
    const modal = new bootstrap.Modal(document.getElementById('sponsorReportModal'));
    modal.show();
}

// ===== وظائف التقارير الجديدة للفترات الزمنية =====

// عرض نموذج تقرير الحوالات للفترة
function showRemittancesPeriodModal() {
    // تعيين التواريخ الافتراضية (آخر 30 يوم)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('remittancesFromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('remittancesToDate').value = today.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('remittancesPeriodModal'));
    modal.show();
}

// عرض نموذج تقرير الديون للفترة
function showDebtsPeriodModal() {
    // تعيين التواريخ الافتراضية (آخر 30 يوم)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('debtsFromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('debtsToDate').value = today.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('debtsPeriodModal'));
    modal.show();
}

// عرض نموذج تقرير الأرصدة للفترة
function showBalancePeriodModal() {
    // تعيين التواريخ الافتراضية (آخر 30 يوم)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('balanceFromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('balanceToDate').value = today.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('balancePeriodModal'));
    modal.show();
}

// تحميل تقرير الحوالات للفترة
async function loadRemittancesPeriodReport() {
    const fromDate = document.getElementById('remittancesFromDate').value;
    const toDate = document.getElementById('remittancesToDate').value;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('remittancesPeriodModal')).hide();
        showEnhancedLoading('report-content', 'جاري تحميل تقرير الحوالات للفترة المحددة');

        const response = await fetch(`${API_BASE_URL}/reports/remittances-period?from_date=${fromDate}&to_date=${toDate}`);
        const data = await response.json();

        if (data.success) {
            displayRemittancesPeriodReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الحوالات: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الحوالات:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحميل تقرير الديون للفترة
async function loadDebtsPeriodReport() {
    const fromDate = document.getElementById('debtsFromDate').value;
    const toDate = document.getElementById('debtsToDate').value;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('debtsPeriodModal')).hide();
        showEnhancedLoading('report-content', 'جاري تحميل تقرير الديون للفترة المحددة');

        const response = await fetch(`${API_BASE_URL}/reports/debts-period?from_date=${fromDate}&to_date=${toDate}`);
        const data = await response.json();

        if (data.success) {
            displayDebtsPeriodReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الديون: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الديون:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحميل تقرير الأرصدة للفترة
async function loadBalancePeriodReport() {
    const fromDate = document.getElementById('balanceFromDate').value;
    const toDate = document.getElementById('balanceToDate').value;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('balancePeriodModal')).hide();
        showEnhancedLoading('report-content', 'جاري تحميل تقرير الأرصدة للفترة المحددة');

        const response = await fetch(`${API_BASE_URL}/reports/balance-period?from_date=${fromDate}&to_date=${toDate}`);
        const data = await response.json();

        if (data.success) {
            displayBalancePeriodReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الأرصدة: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الأرصدة:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحميل الملخص الإجمالي المحسن مع تحسينات الأداء
async function loadSummaryReport() {
    try {
        // بدء مراقبة الأداء
        if (window.performanceOptimizations) {
            window.performanceOptimizations.performanceMonitor.startTimer('summary-report-load');
        }

        showEnhancedLoading('report-content', 'جاري تحميل الملخص الإجمالي المحسن');

        // استخدام الطلب المحسن مع التخزين المؤقت
        const data = window.performanceOptimizations ?
            await window.performanceOptimizations.optimizedFetch(`${API_BASE_URL}/reports/summary`) :
            await fetch(`${API_BASE_URL}/reports/summary`).then(r => r.json());

        if (data.success) {
            displayEnhancedSummaryReport(data.data);

            // انتهاء مراقبة الأداء
            if (window.performanceOptimizations) {
                window.performanceOptimizations.performanceMonitor.endTimer('summary-report-load');
            }
        } else {
            showError('فشل في تحميل التقرير');
        }
    } catch (error) {
        console.error('خطأ في تحميل الملخص الإجمالي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض الملخص الإجمالي
function displaySummaryReport(data) {
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الملخص الإجمالي
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-primary">${data.totals.sponsors_count}</h3>
                            <p class="text-muted">إجمالي الرعاة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-danger">${formatCurrency(data.totals.total_debts)}</h3>
                            <p class="text-muted">إجمالي الديون</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-success">${formatCurrency(data.totals.total_remittances)}</h3>
                            <p class="text-muted">إجمالي الحوالات</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-warning">${formatCurrency(data.totals.total_balance)}</h3>
                            <p class="text-muted">الرصيد المتبقي</p>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الرعاة -->
                <h6 class="mb-3">تفاصيل الرعاة:</h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-secondary">
                            <tr>
                                <th>الراعي</th>
                                <th>إجمالي الديون</th>
                                <th>إجمالي الحوالات</th>
                                <th>الرصيد المتبقي</th>
                                <th class="no-print">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    data.sponsors.forEach(sponsor => {
        const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';
        html += `
            <tr>
                <td><strong>${sponsor.display_name || sponsor.name}</strong></td>
                <td class="text-danger text-currency">${formatCurrency(sponsor.total_debts)}</td>
                <td class="text-success text-currency">${formatCurrency(sponsor.total_remittances)}</td>
                <td class="${balanceClass} text-currency"><strong>${formatCurrency(sponsor.balance)}</strong></td>
                <td class="no-print">
                    <button class="btn btn-outline-primary btn-sm" 
                            onclick="showSponsorDebts(${sponsor.id}, '${sponsor.display_name || sponsor.name}')"
                            title="عرض ديون الراعي">
                        <i class="fas fa-eye me-1"></i>عرض الديون
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    document.getElementById('report-content').innerHTML = html;
}

// عرض ديون راعي محدد
async function showSponsorDebts(sponsorId, sponsorName) {
    try {
        showLoading('report-content');
        
        const response = await fetch(`${API_BASE_URL}/debts?sponsor_id=${sponsorId}`);
        const data = await response.json();
        
        if (data.success) {
            displaySponsorDebtsReport(data.data, sponsorName);
        } else {
            showError('فشل في تحميل ديون الراعي');
        }
    } catch (error) {
        console.error('خطأ في تحميل ديون الراعي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض تقرير ديون راعي محدد
function displaySponsorDebtsReport(debts, sponsorName) {
    let totalDebts = 0;
    
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    ديون الراعي: ${sponsorName}
                </h5>
                <div class="no-print">
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="loadSummaryReport()">
                        <i class="fas fa-arrow-left me-1"></i>العودة للتقرير
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
    `;
    
    if (debts.length === 0) {
        html += `
            <div class="text-center text-muted py-4">
                <i class="fas fa-info-circle fa-3x mb-3"></i>
                <h5>لا توجد ديون مسجلة</h5>
                <p>لم يتم تسجيل أي ديون لهذا الراعي</p>
            </div>
        `;
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-secondary">
                        <tr>
                            <th>التاريخ</th>
                            <th>الوصف</th>
                            <th>الفرع</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        debts.forEach(debt => {
            totalDebts += debt.amount;
            html += `
                <tr>
                    <td>${formatDate(debt.date)}</td>
                    <td>${debt.description || '-'}</td>
                    <td>${debt.branch_name || '-'}</td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">إجمالي الديون:</th>
                            <th class="text-danger text-currency">${formatCurrency(totalDebts)}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;
    }
    
    html += `
            </div>
        </div>
    `;
    
    document.getElementById('report-content').innerHTML = html;
}

// عرض نافذة تقرير بطاقات الرعاة
function showSponsorCardsModal() {
    const modal = new bootstrap.Modal(document.getElementById('sponsorCardsModal'));
    modal.show();
    
    // تعيين التواريخ الافتراضية (الشهر الحالي)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    document.getElementById('cardsFromDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('cardsToDate').value = lastDay.toISOString().split('T')[0];
}

// إنشاء بطاقات الرعاة
async function generateSponsorCards() {
    const fromDate = document.getElementById('cardsFromDate').value;
    const toDate = document.getElementById('cardsToDate').value;
    
    if (!fromDate || !toDate) {
        alert('يرجى تحديد الفترة الزمنية');
        return;
    }
    
    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }
    
    try {
        bootstrap.Modal.getInstance(document.getElementById('sponsorCardsModal')).hide();
        showLoading('report-content');
        
        // جلب بيانات الرعاة والديون للفترة المحددة
        const [sponsorsResponse, debtsResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/sponsors`),
            fetch(`${API_BASE_URL}/debts?from_date=${fromDate}&to_date=${toDate}`)
        ]);
        
        const sponsorsData = await sponsorsResponse.json();
        const debtsData = await debtsResponse.json();
        
        if (sponsorsData.success && debtsData.success) {
            displaySponsorCards(sponsorsData.data, debtsData.data, fromDate, toDate);
        } else {
            showError('فشل في تحميل بيانات البطاقات');
        }
    } catch (error) {
        console.error('خطأ في إنشاء بطاقات الرعاة:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض بطاقات الرعاة
function displaySponsorCards(sponsors, debts, fromDate, toDate) {
    // تجميع الديون حسب الراعي
    const sponsorDebts = {};
    debts.forEach(debt => {
        if (!sponsorDebts[debt.sponsor_id]) {
            sponsorDebts[debt.sponsor_id] = [];
        }
        sponsorDebts[debt.sponsor_id].push(debt);
    });
    
    let html = `
        <div class="sponsor-cards-container">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center no-print">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>
                        بطاقات الرعاة للفترة من ${formatDate(fromDate)} إلى ${formatDate(toDate)}
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="printSponsorCards()">
                        <i class="fas fa-print me-2"></i>طباعة البطاقات
                    </button>
                </div>
                <div class="card-body p-0">
    `;
    
    // إنشاء البطاقات (4 بطاقات في كل صفحة)
    let cardCount = 0;
    let pageHtml = '';
    
    // فلترة الرعاة ليظهر فقط من لهم ديون في الفترة المحددة
    const sponsorsWithDebts = sponsors.filter(sponsor => {
        const sponsorDebtsData = sponsorDebts[sponsor.id] || [];
        return sponsorDebtsData.length > 0;
    });
    
    if (sponsorsWithDebts.length === 0) {
        html += `
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد ديون في الفترة المحددة</h5>
                        <p class="text-muted">لم يتم العثور على رعاة لديهم ديون في الفترة من ${formatDate(fromDate)} إلى ${formatDate(toDate)}</p>
                    </div>
                </div>
            </div>
        </div>
        `;
        document.getElementById('content').innerHTML = html;
        return;
    }

    sponsorsWithDebts.forEach(sponsor => {
        const sponsorDebtsData = sponsorDebts[sponsor.id] || [];
        const totalDebts = sponsorDebtsData.reduce((sum, debt) => sum + debt.amount, 0);
        
        // بدء صفحة جديدة كل 4 بطاقات (2x2 في الصفحة)
        if (cardCount % 4 === 0) {
            if (cardCount > 0) {
                html += `</div><div class="page-break"></div><div class="cards-page d-flex flex-wrap">`;
            } else {
                html += `<div class="cards-page d-flex flex-wrap">`;
            }
        }
        
        pageHtml = `
            <div class="sponsor-card">
                <div class="card-header-custom">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        ${sponsor.display_name || sponsor.full_name || sponsor.name}
                    </h6>
                    <small class="text-muted">الفترة: ${formatDate(fromDate)} - ${formatDate(toDate)}</small>
                </div>
                <div class="card-body-custom">
                    <div class="sponsor-info mb-2">
                        <div class="row">
                            <div class="col-6">
                                <strong>الهاتف:</strong> ${sponsor.phone || 'غير محدد'}
                            </div>
                            <div class="col-6">
                                <strong>إجمالي الديون:</strong> 
                                <span class="text-danger">${formatCurrency(totalDebts)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="debts-list">
                        <h6 class="mb-2">تفاصيل الديون:</h6>
        `;
        
        if (sponsorDebtsData.length === 0) {
            pageHtml += `
                        <p class="text-muted text-center">لا توجد ديون في هذه الفترة</p>
            `;
        } else {
            pageHtml += `
                        <table class="table table-sm table-borderless">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            // تجميع الديون حسب التاريخ
            const debtsByDate = {};
            sponsorDebtsData.forEach(debt => {
                const dateKey = debt.date;
                if (!debtsByDate[dateKey]) {
                    debtsByDate[dateKey] = [];
                }
                debtsByDate[dateKey].push(debt);
            });
            
            // عرض الديون المجمعة
            Object.keys(debtsByDate).sort().forEach(date => {
                const debtsForDate = debtsByDate[date];
                const totalAmount = debtsForDate.reduce((sum, debt) => sum + debt.amount, 0);
                
                // دمج الأوصاف مع عرض المبلغ لكل وصف
                const combinedDescription = debtsForDate.map(debt => {
                    const desc = debt.description || 'بدون وصف';
                    return `${desc} (${formatCurrency(debt.amount)})`;
                }).join(' + ');
                
                pageHtml += `
                                <tr>
                                    <td>${formatDate(date)}</td>
                                    <td class="text-danger fw-bold">${formatCurrency(totalAmount)}</td>
                                    <td class="small">${combinedDescription}</td>
                                </tr>
                `;
            });
            
            pageHtml += `
                            </tbody>
                        </table>
            `;
        }
        
        pageHtml += `
                    </div>
                </div>
            </div>
        `;
        
        html += pageHtml;
        cardCount++;
    });
    
    html += `
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('report-content').innerHTML = html;
}

// طباعة بطاقات الرعاة
function printSponsorCards() {
    // الحصول على محتوى البطاقات فقط
    const cardsContainer = document.querySelector('.sponsor-cards-container');
    if (!cardsContainer) {
        alert('لا توجد بطاقات للطباعة');
        return;
    }
    
    // إنشاء نافذة طباعة منفصلة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    
    const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>بطاقات الرعاة</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    direction: rtl;
                    margin: 0;
                    padding: 10px;
                }
                
                .cards-page {
                    display: flex !important;
                    flex-wrap: wrap !important;
                    justify-content: space-between !important;
                    align-items: flex-start !important;
                    page-break-after: always;
                    min-height: 95vh;
                }
                
                .cards-page:last-child {
                    page-break-after: avoid;
                }
                
                .sponsor-card {
                    width: 48% !important;
                    min-height: 60vh !important;
                    height: auto !important;
                    margin-bottom: 2vh !important;
                    border: 2px solid #28a745 !important;
                    border-radius: 8px !important;
                    page-break-inside: avoid !important;
                    overflow: visible !important;
                    display: flex !important;
                    flex-direction: column !important;
                }
                
                .card-header-custom {
                    background: linear-gradient(135deg, #28a745, #20c997) !important;
                    color: white !important;
                    padding: 8px 12px !important;
                    border-bottom: none !important;
                    flex-shrink: 0 !important;
                }
                
                .card-header-custom h6 {
                    margin: 0 !important;
                    font-size: 14px !important;
                    font-weight: bold !important;
                }
                
                .card-header-custom small {
                    font-size: 11px !important;
                    opacity: 0.9 !important;
                }
                
                .card-body-custom {
                    padding: 10px !important;
                    flex: 1 !important;
                    overflow: visible !important;
                    font-size: 12px !important;
                }
                
                .sponsor-info {
                    background-color: #f8f9fa !important;
                    padding: 8px !important;
                    border-radius: 4px !important;
                    margin-bottom: 8px !important;
                }
                
                .debts-list h6 {
                    font-size: 13px !important;
                    color: #495057 !important;
                    margin-bottom: 6px !important;
                }
                
                .table-sm {
                    font-size: 11px !important;
                }
                
                .table-sm th,
                .table-sm td {
                    padding: 3px 6px !important;
                    border-bottom: 1px solid #dee2e6 !important;
                }
                
                .text-danger {
                    color: #dc3545 !important;
                }
                
                .text-muted {
                    color: #6c757d !important;
                }
                
                .page-break {
                    display: none;
                }
                
                @media print {
                    body { 
                        margin: 0 !important; 
                        padding: 5px !important;
                    }
                    
                    .cards-page {
                        page-break-after: always !important;
                    }
                    
                    .cards-page:last-child {
                        page-break-after: avoid !important;
                    }
                    
                    .sponsor-card {
                        page-break-inside: avoid !important;
                    }
                }
            </style>
        </head>
        <body>
            ${cardsContainer.innerHTML}
        </body>
        </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    
    // انتظار تحميل المحتوى ثم الطباعة
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
}

// تحميل التقرير اليومي
async function loadDailyReport() {
    const date = document.getElementById('reportDate').value;
    if (!date) {
        alert('يرجى اختيار التاريخ');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('dailyReportModal')).hide();
        showLoading('report-content');

        const response = await fetch(`${API_BASE_URL}/reports/daily?date=${date}`);
        const data = await response.json();

        if (data.success) {
            displayDailyReport(data.data);
        } else {
            showError('فشل في تحميل التقرير اليومي');
        }
    } catch (error) {
        console.error('خطأ في تحميل التقرير اليومي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض التقرير اليومي
function displayDailyReport(data) {
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    التقرير اليومي - ${formatDate(data.date)}
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
            <div class="card-body">
                <!-- الإحصائيات اليومية -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-danger">${formatCurrency(data.totals.daily_debts)}</h4>
                            <p class="text-muted">إجمالي الديون اليومية</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-success">${formatCurrency(data.totals.daily_remittances)}</h4>
                            <p class="text-muted">إجمالي الحوالات اليومية</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-warning">${formatCurrency(data.totals.net_change)}</h4>
                            <p class="text-muted">صافي التغيير</p>
                        </div>
                    </div>
                </div>
    `;

    // عرض الديون اليومية
    if (data.debts.length > 0) {
        html += `
                <h6 class="mb-3 text-danger">
                    <i class="fas fa-minus-circle me-2"></i>
                    الديون (${data.debts.length})
                </h6>
                <div class="table-responsive mb-4">
                    <table class="table table-striped">
                        <thead class="table-secondary">
                            <tr>
                                <th>الراعي</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                                <th class="no-print">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.debts.forEach(debt => {
            html += `
                <tr>
                    <td>${debt.sponsor_name}</td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                    <td class="no-print">
                        <button class="btn btn-outline-primary btn-sm" 
                                onclick="showSponsorDebts(${debt.sponsor_id}, '${debt.sponsor_name}')"
                                title="عرض جميع ديون الراعي">
                            <i class="fas fa-eye me-1"></i>عرض الديون
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    // عرض الحوالات اليومية
    if (data.remittances.length > 0) {
        html += `
                <h6 class="mb-3 text-success">
                    <i class="fas fa-plus-circle me-2"></i>
                    الحوالات (${data.remittances.length})
                </h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-secondary">
                            <tr>
                                <th>الراعي</th>
                                <th>المبلغ</th>
                                <th>المصدر</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${remittance.sponsor_name}</td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    if (data.debts.length === 0 && data.remittances.length === 0) {
        html += '<p class="text-muted text-center">لا توجد معاملات في هذا التاريخ</p>';
    }

    html += '</div></div>';

    document.getElementById('report-content').innerHTML = html;
}

// تحميل تقرير الراعي
async function loadSponsorReport() {
    const sponsorId = document.getElementById('reportSponsor').value;
    if (!sponsorId) {
        showError('يرجى اختيار الراعي');
        return;
    }

    // إغلاق المودال أولاً
    const modal = bootstrap.Modal.getInstance(document.getElementById('sponsorReportModal'));
    if (modal) {
        modal.hide();
    }

    // عرض شاشة التحميل
    showLoading('report-content');

    // جمع معاملات التاريخ
    const fromDate = document.getElementById('reportFromDate').value;
    const toDate = document.getElementById('reportToDate').value;
    
    let url = `/api/reports/sponsor/${sponsorId}`;
    const params = new URLSearchParams();
    
    if (fromDate) params.append('from_date', fromDate);
    if (toDate) params.append('to_date', toDate);
    
    if (params.toString()) {
        url += '?' + params.toString();
    }

    try {
        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            displaySponsorReport(data.data, fromDate, toDate);
        } else {
            showError('فشل في تحميل تقرير الراعي');
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الراعي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض تقرير الراعي
function displaySponsorReport(data, fromDate = null, toDate = null) {
    const sponsor = data.sponsor;
    const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';

    // إنشاء نص الفلتر
    let filterText = '';
    if (fromDate || toDate) {
        filterText = '<div class="alert alert-info mb-3"><i class="fas fa-filter me-2"></i>';
        if (fromDate && toDate) {
            filterText += `الفترة: من ${fromDate} إلى ${toDate}`;
        } else if (fromDate) {
            filterText += `من تاريخ: ${fromDate}`;
        } else if (toDate) {
            filterText += `حتى تاريخ: ${toDate}`;
        }
        filterText += '</div>';
    }

    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تقرير الراعي - ${sponsor.name}
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
            <div class="card-body">
                ${filterText}
                <!-- معلومات الراعي -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات الراعي:</h6>
                        <p><strong>الاسم:</strong> ${sponsor.name}</p>
                        <p><strong>الهاتف:</strong> ${sponsor.phone || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${sponsor.address || 'غير محدد'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>الملخص المالي:</h6>
                        <p><strong class="text-danger">إجمالي الديون:</strong> ${formatCurrency(sponsor.total_debts)}</p>
                        <p><strong class="text-success">إجمالي الحوالات:</strong> ${formatCurrency(sponsor.total_remittances)}</p>
                        <p><strong class="${balanceClass}">الرصيد المتبقي:</strong> ${formatCurrency(sponsor.balance)}</p>
                    </div>
                </div>
    `;

    // عرض الديون
    if (data.debts.length > 0) {
        html += `
                <h6 class="mb-3 text-danger">
                    <i class="fas fa-minus-circle me-2"></i>
                    الديون (${data.debts.length})
                </h6>
                <div class="table-responsive mb-4">
                    <table class="table table-striped">
                        <thead class="table-secondary">
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.debts.forEach(debt => {
            html += `
                <tr>
                    <td>${formatDate(debt.date)}</td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    // عرض الحوالات
    if (data.remittances.length > 0) {
        html += `
                <h6 class="mb-3 text-success">
                    <i class="fas fa-plus-circle me-2"></i>
                    الحوالات (${data.remittances.length})
                </h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-secondary">
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>المصدر</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${formatDate(remittance.date)}</td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    if (data.debts.length === 0 && data.remittances.length === 0) {
        html += '<p class="text-muted text-center">لا توجد معاملات لهذا الراعي</p>';
    }

    html += '</div></div>';

    document.getElementById('report-content').innerHTML = html;
}

// وظيفة طباعة محسنة وموحدة
function printReport() {
    const reportContent = document.getElementById('report-content');
    if (!reportContent) {
        showError('لا يوجد تقرير للطباعة');
        return;
    }

    // حفظ المحتوى الأصلي
    const originalContent = document.body.innerHTML;
    
    // إنشاء CSS مخصص للطباعة
    const printStyles = `
        <style id="print-styles">
            @media print {
                @page {
                    margin: 1cm;
                    size: A4;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #333;
                    background: white;
                    margin: 0;
                    padding: 0;
                }
                
                /* ترويسة مدمجة ومختصرة */
                .print-header {
                    text-align: center;
                    margin-bottom: 20px;
                    padding-bottom: 15px;
                    border-bottom: 2px solid #007bff;
                }
                .print-header h1 {
                    color: #007bff;
                    font-size: 20px;
                    margin: 0 0 8px 0;
                    font-weight: bold;
                }
                .print-header p {
                    color: #666;
                    font-size: 12px;
                    margin: 3px 0;
                }
                
                /* تنسيق المحتوى */
                .card {
                    border: 1px solid #ddd;
                    margin-bottom: 20px;
                    page-break-inside: avoid;
                    border-radius: 5px;
                }
                .card-header {
                    background: #f8f9fa;
                    padding: 12px 15px;
                    font-weight: bold;
                    font-size: 14px;
                    border-bottom: 1px solid #ddd;
                    border-radius: 5px 5px 0 0;
                }
                .card-body {
                    padding: 15px;
                }
                .table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 11px;
                    margin-bottom: 15px;
                }
                .table th, .table td {
                    border: 1px solid #ddd;
                    padding: 8px 6px;
                    text-align: right;
                }
                .table th {
                    background: #f8f9fa;
                    font-weight: bold;
                    color: #495057;
                }
                .table-striped tbody tr:nth-of-type(odd) {
                    background: #f9f9f9;
                }
                .stat-card {
                    background: #f8f9fa;
                    border: 1px solid #ddd;
                    padding: 15px;
                    text-align: center;
                    margin-bottom: 15px;
                    display: inline-block;
                    width: 180px;
                    margin-left: 15px;
                    border-radius: 5px;
                }
                .stat-number {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .stat-label {
                    font-size: 11px;
                    color: #666;
                }
                .row {
                    display: flex;
                    flex-wrap: wrap;
                    margin: -10px;
                }
                .col-md-3, .col-md-4, .col-md-6 {
                    padding: 10px;
                    flex: 1;
                    min-width: 200px;
                }
                
                /* إخفاء الأزرار */
                .btn, .btn-group, button, .no-print { 
                    display: none !important; 
                }
                
                /* تحسين الألوان للطباعة */
                .text-danger { color: #dc3545 !important; }
                .text-success { color: #28a745 !important; }
                .text-primary { color: #007bff !important; }
                .text-warning { color: #856404 !important; }
                .text-info { color: #17a2b8 !important; }
                .text-muted { color: #6c757d !important; }
                .badge {
                    display: inline-block;
                    padding: 4px 8px;
                    font-size: 11px;
                    border-radius: 3px;
                    color: white;
                }
                .bg-primary { background-color: #007bff !important; }
                .bg-success { background-color: #28a745 !important; }
                .bg-danger { background-color: #dc3545 !important; }
                .bg-warning { background-color: #ffc107 !important; color: #212529 !important; }
                .bg-info { background-color: #17a2b8 !important; }
            }
        </style>
    `;

    // إضافة الستايل للصفحة
    document.head.insertAdjacentHTML('beforeend', printStyles);

    // استبدال محتوى الصفحة بالكامل للطباعة
    document.body.innerHTML = `
        <div class="print-header">
            <h1>نظام المحاسبة للرعاة</h1>
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</p>
        </div>
        ${reportContent.innerHTML}
    `;

    // تنفيذ الطباعة
    window.print();

    // استعادة المحتوى الأصلي بعد الطباعة
    setTimeout(() => {
        document.body.innerHTML = originalContent;
        // إزالة الستايل
        const styles = document.getElementById('print-styles');
        if (styles) styles.remove();
        
        // إعادة تحميل الأحداث إذا لزم الأمر
        if (typeof initializeApp === 'function') {
            initializeApp();
        }
    }, 1000);
}

// وظيفة طباعة محسنة للتقارير المتقدمة
function printEnhancedReport() {
    printReport(); // استخدام نفس الوظيفة المحسنة
}

// وظيفة تصدير PDF محسنة
async function exportReport() {
    try {
        const reportElement = document.getElementById('report-content');
        if (!reportElement) {
            showError('لا يوجد تقرير لتصديره');
            return;
        }

        // إنشاء نسخة للتصدير
        const exportElement = reportElement.cloneNode(true);
        
        // إزالة الأزرار والعناصر غير المرغوبة
        const buttons = exportElement.querySelectorAll('.btn, .btn-group, button');
        buttons.forEach(btn => btn.remove());
        
        // إضافة العنصر مؤقتاً للصفحة
        exportElement.style.position = 'absolute';
        exportElement.style.left = '-9999px';
        exportElement.style.background = 'white';
        exportElement.style.padding = '20px';
        exportElement.style.width = '800px';
        document.body.appendChild(exportElement);

        // تحويل إلى صورة
        const canvas = await html2canvas(exportElement, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
        });

        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');
        
        // إضافة الترويسة
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(20);
        pdf.text('نظام المحاسبة للرعاة', 105, 20, { align: 'center' });
        
        pdf.setFont('helvetica', 'normal');
        pdf.setFontSize(12);
        const currentDate = new Date().toLocaleDateString('ar-SA');
        pdf.text(`تاريخ التصدير: ${currentDate}`, 105, 30, { align: 'center' });

        // إضافة الصورة
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 170;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        
        let yPosition = 40;
        const pageHeight = 280;
        
        if (imgHeight > pageHeight - yPosition) {
            // تقسيم على عدة صفحات
            let remainingHeight = imgHeight;
            let sourceY = 0;
            
            while (remainingHeight > 0) {
                const sliceHeight = Math.min(pageHeight - yPosition, remainingHeight);
                const sliceCanvas = document.createElement('canvas');
                const sliceCtx = sliceCanvas.getContext('2d');
                
                sliceCanvas.width = canvas.width;
                sliceCanvas.height = (sliceHeight * canvas.width) / imgWidth;
                
                sliceCtx.drawImage(canvas, 0, sourceY, canvas.width, sliceCanvas.height, 0, 0, canvas.width, sliceCanvas.height);
                
                const sliceImgData = sliceCanvas.toDataURL('image/png');
                pdf.addImage(sliceImgData, 'PNG', 20, yPosition, imgWidth, sliceHeight);
                
                remainingHeight -= sliceHeight;
                sourceY += sliceCanvas.height;
                
                if (remainingHeight > 0) {
                    pdf.addPage();
                    yPosition = 20;
                }
            }
        } else {
            pdf.addImage(imgData, 'PNG', 20, yPosition, imgWidth, imgHeight);
        }

        // حفظ الملف
        const fileName = `تقرير_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);
        
        // إزالة العنصر المؤقت
        document.body.removeChild(exportElement);
        
        showSuccess('تم تصدير التقرير بنجاح');
        
    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        showError('فشل في تصدير التقرير');
    }
}

// وظيفة تصدير PDF للتقارير المتقدمة
function exportToPDF(reportType) {
    exportReport(); // استخدام نفس الوظيفة المحسنة
}

// === وظائف إدارة الفروع ===

// عرض نموذج إضافة فرع جديد
function showAddBranchModal() {
    document.getElementById('branchModalTitle').textContent = 'إضافة فرع جديد';
    document.getElementById('branchForm').reset();
    document.getElementById('branchId').value = '';
    document.getElementById('branchIsActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('branchModal'));
    modal.show();
}

// حفظ الفرع (إضافة أو تعديل)
async function saveBranch() {
    const form = document.getElementById('branchForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const branchId = document.getElementById('branchId').value;
    const branchData = {
        name: document.getElementById('branchName').value.trim(),
        code: document.getElementById('branchCode').value.trim(),
        description: document.getElementById('branchDescription').value.trim(),
        manager_name: document.getElementById('branchManagerName').value.trim(),
        phone: document.getElementById('branchPhone').value.trim(),
        address: document.getElementById('branchAddress').value.trim(),
        is_active: document.getElementById('branchIsActive').checked
    };

    try {
        const url = branchId ? `${API_BASE_URL}/branches/${branchId}` : `${API_BASE_URL}/branches`;
        const method = branchId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(branchData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(branchId ? 'تم تحديث الفرع بنجاح' : 'تم إضافة الفرع بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('branchModal')).hide();
            loadBranches(); // إعادة تحميل قائمة الفروع
        } else {
            showError(data.message || 'فشل في حفظ الفرع');
        }
    } catch (error) {
        console.error('خطأ في حفظ الفرع:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تعديل فرع
async function editBranch(branchId) {
    try {
        const response = await fetch(`${API_BASE_URL}/branches/${branchId}`);
        const data = await response.json();

        if (data.success) {
            const branch = data.data;
            
            document.getElementById('branchModalTitle').textContent = 'تعديل الفرع';
            document.getElementById('branchId').value = branch.id;
            document.getElementById('branchName').value = branch.name;
            document.getElementById('branchCode').value = branch.code;
            document.getElementById('branchDescription').value = branch.description || '';
            document.getElementById('branchManagerName').value = branch.manager_name || '';
            document.getElementById('branchPhone').value = branch.phone || '';
            document.getElementById('branchAddress').value = branch.address || '';
            document.getElementById('branchIsActive').checked = branch.is_active;
            
            const modal = new bootstrap.Modal(document.getElementById('branchModal'));
            modal.show();
        } else {
            showError('فشل في تحميل بيانات الفرع');
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات الفرع:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض تفاصيل الفرع
async function viewBranchDetails(branchId) {
    try {
        const response = await fetch(`${API_BASE_URL}/branches/${branchId}/details`);
        const data = await response.json();

        if (data.success) {
            displayBranchDetailsModal(data.data);
        } else {
            showError('فشل في تحميل تفاصيل الفرع');
        }
    } catch (error) {
        console.error('خطأ في تحميل تفاصيل الفرع:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض تفاصيل الفرع في النموذج
function displayBranchDetailsModal(branchData) {
    const branch = branchData.branch;
    const debts = branchData.debts;
    
    document.getElementById('branchDetailsTitle').textContent = `تفاصيل فرع: ${branch.name}`;
    
    let html = `
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفرع</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr><td><strong>اسم الفرع:</strong></td><td>${branch.name}</td></tr>
                            <tr><td><strong>رمز الفرع:</strong></td><td><code>${branch.code}</code></td></tr>
                            <tr><td><strong>المدير:</strong></td><td>${branch.manager_name || '-'}</td></tr>
                            <tr><td><strong>الهاتف:</strong></td><td>${branch.phone || '-'}</td></tr>
                            <tr><td><strong>العنوان:</strong></td><td>${branch.address || '-'}</td></tr>
                            <tr><td><strong>الوصف:</strong></td><td>${branch.description || '-'}</td></tr>
                            <tr><td><strong>الحالة:</strong></td><td>
                                ${branch.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>'}
                            </td></tr>
                            <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${formatDate(branch.created_at)}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات الفرع</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-danger">${branch.debts_count || 0}</h4>
                                    <small class="text-muted">عدد الديون</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-danger">${formatCurrency(branch.total_debts || 0)}</h4>
                                <small class="text-muted">إجمالي الديون</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    if (debts && debts.length > 0) {
        html += `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-minus-circle me-2"></i>
                        ديون الفرع (${debts.length})
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-secondary">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الراعي</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        debts.forEach(debt => {
            html += `
                <tr>
                    <td>${formatDate(debt.date)}</td>
                    <td><strong>${debt.sponsor_name}</strong></td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div></div></div>';
    } else {
        html += `
            <div class="card">
                <div class="card-body text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>لا توجد ديون مسجلة لهذا الفرع</p>
                </div>
            </div>
        `;
    }

    document.getElementById('branchDetailsContent').innerHTML = html;
    
    const modal = new bootstrap.Modal(document.getElementById('branchDetailsModal'));
    modal.show();
}

// تبديل حالة الفرع (تفعيل/تعطيل)
async function toggleBranchStatus(branchId, currentStatus) {
    const action = currentStatus ? 'تعطيل' : 'تفعيل';
    
    if (!confirm(`هل أنت متأكد من ${action} هذا الفرع؟`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/branches/${branchId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_active: !currentStatus
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(`تم ${action} الفرع بنجاح`);
            loadBranches(); // إعادة تحميل قائمة الفروع
        } else {
            showError(data.message || `فشل في ${action} الفرع`);
        }
    } catch (error) {
        console.error(`خطأ في ${action} الفرع:`, error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// === وظائف الإدخال المجمع للديون ===

// عرض نموذج الإدخال المجمع للديون
function showBulkDebtModal() {
    document.getElementById('bulkDebtForm').reset();
    document.getElementById('bulkDebtDate').value = new Date().toISOString().split('T')[0];
    
    // إعادة تعيين حاوي الديون إلى صف واحد
    const container = document.getElementById('bulkDebtsContainer');
    container.innerHTML = createBulkDebtRow();
    
    const modal = new bootstrap.Modal(document.getElementById('bulkDebtModal'));
    modal.show();
    
    // التركيز التلقائي على أول حقل راعي
    setTimeout(() => {
        const firstSponsorField = container.querySelector('.bulk-debt-sponsor');
        if (firstSponsorField) firstSponsorField.focus();
    }, 300);
}

// إنشاء صف دين جديد للإدخال المجمع
function createBulkDebtRow() {
    let html = `
        <div class="bulk-debt-row row mb-2">
            <div class="col-md-5">
                <div class="input-group">
                    <input type="text" class="form-control bulk-debt-sponsor" 
                           placeholder="اكتب اسم الراعي..." required
                           data-sponsor-id=""
                           oninput="handleSponsorAutocomplete(this)"
                           onkeydown="handleBulkDebtNavigation(event, this)"
                           onblur="validateSponsorInput(this)">
                    <button class="btn btn-outline-primary" type="button" 
                            onclick="showQuickAddSponsorModal(this)" title="إضافة راعي جديد">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <input type="number" class="form-control bulk-debt-amount" 
                       placeholder="المبلغ (بالآلاف)" min="0" step="1" required
                       onkeydown="handleBulkDebtNavigation(event, this)">
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-danger btn-sm" 
                        onclick="removeBulkDebtRow(this)" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    return html;
}

// معالجة اختيار الراعي في الإدخال المجمع
function handleSponsorSelection(selectElement) {
    if (selectElement.value === 'new_sponsor') {
        showQuickAddSponsorModal(selectElement);
        selectElement.value = ''; // إعادة تعيين القيمة
    }
}

// عرض النافذة المنبثقة لإضافة راعي جديد
function showQuickAddSponsorModal(triggerElement) {
    // حفظ مرجع للعنصر المحفز
    window.currentBulkSponsorSelect = triggerElement.closest('.bulk-debt-row').querySelector('.bulk-debt-sponsor');
    
    // إنشاء النافذة المنبثقة
    const modalHtml = `
        <div class="modal fade" id="quickAddSponsorModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة راعي جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="quickSponsorForm">
                            <div class="mb-3">
                                <label for="quickSponsorName" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="quickSponsorName" required 
                                       placeholder="الاسم المستخدم في الإدخال السريع">
                            </div>
                            <div class="mb-3">
                                <label for="quickSponsorFullName" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="quickSponsorFullName" 
                                       placeholder="الاسم الكامل للتقارير (اختياري)">
                            </div>
                            <div class="mb-3">
                                <label for="quickSponsorPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="quickSponsorPhone">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveQuickSponsor()">
                            <i class="fas fa-save me-2"></i>حفظ وإضافة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // إزالة النافذة السابقة إن وجدت
    const existingModal = document.getElementById('quickAddSponsorModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // إضافة النافذة الجديدة
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // عرض النافذة
    const modal = new bootstrap.Modal(document.getElementById('quickAddSponsorModal'));
    modal.show();
    
    // التركيز على حقل الاسم
    setTimeout(() => {
        document.getElementById('quickSponsorName').focus();
    }, 300);
}

// حفظ الراعي الجديد من النافذة المنبثقة
async function saveQuickSponsor() {
    const name = document.getElementById('quickSponsorName').value.trim();
    const fullName = document.getElementById('quickSponsorFullName').value.trim();
    const phone = document.getElementById('quickSponsorPhone').value.trim();

    if (!name) {
        alert('الاسم الأول مطلوب');
        document.getElementById('quickSponsorName').focus();
        return;
    }

    const sponsorData = { 
        name, 
        full_name: fullName || name,
        phone 
    };

    try {
        const response = await fetch(`${API_BASE_URL}/sponsors`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(sponsorData)
        });

        const data = await response.json();

        if (data.success) {
            // إضافة الراعي الجديد لقائمة الرعاة
            const newSponsor = data.data;
            sponsors.push(newSponsor);
            
            // تحديث جميع قوائم الرعاة في الإدخال المجمع
            updateBulkSponsorSelects();
            
            // اختيار الراعي الجديد في العنصر المحفز
            if (window.currentBulkSponsorSelect) {
                window.currentBulkSponsorSelect.value = newSponsor.id;
            }
            
            // إغلاق النافذة
            bootstrap.Modal.getInstance(document.getElementById('quickAddSponsorModal')).hide();
            
            showSuccess(`تم إضافة الراعي "${name}" بنجاح`);
            
            // التركيز على حقل المبلغ
            if (window.currentBulkSponsorSelect) {
                const amountField = window.currentBulkSponsorSelect.closest('.bulk-debt-row').querySelector('.bulk-debt-amount');
                if (amountField) amountField.focus();
            }
            
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الراعي:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// معالجة الإكمال التلقائي للرعاة
function handleSponsorAutocomplete(input) {
    const inputValue = input.value.toLowerCase().trim();
    
    if (inputValue.length === 0) {
        input.setAttribute('data-sponsor-id', '');
        return;
    }
    
    // البحث عن أول تطابق في الاسم الأول (name) وليس الاسم الكامل
    const matchingSponsor = sponsors.find(sponsor => {
        const sponsorName = sponsor.name; // استخدام الاسم الأول فقط
        return sponsorName && sponsorName.toLowerCase().startsWith(inputValue);
    });
    
    if (matchingSponsor) {
        const sponsorName = matchingSponsor.name;
        
        // إكمال النص تلقائياً
        input.value = sponsorName;
        input.setAttribute('data-sponsor-id', matchingSponsor.id);
        
        // تحديد النص المكمل للمستخدم
        const selectionStart = inputValue.length;
        const selectionEnd = sponsorName.length;
        
        setTimeout(() => {
            input.setSelectionRange(selectionStart, selectionEnd);
        }, 0);
    } else {
        input.setAttribute('data-sponsor-id', '');
    }
}

// التحقق من صحة إدخال الراعي
function validateSponsorInput(input) {
    const sponsorId = input.getAttribute('data-sponsor-id');
    
    if (!sponsorId && input.value.trim()) {
        // إذا لم يتم العثور على راعي مطابق، مسح الحقل
        input.value = '';
        input.setAttribute('data-sponsor-id', '');
        input.style.borderColor = '#dc3545';
        
        setTimeout(() => {
            input.style.borderColor = '';
        }, 2000);
    }
}

// معالجة التنقل بلوحة المفاتيح في الإدخال المجمع
function handleBulkDebtNavigation(event, currentElement) {
    const key = event.key;
    
    if (key === 'Tab' || key === 'Enter') {
        // للحقول النصية، التأكد من وجود راعي صحيح
        if (currentElement.classList.contains('bulk-debt-sponsor')) {
            const sponsorId = currentElement.getAttribute('data-sponsor-id');
            if (!sponsorId && currentElement.value.trim()) {
                event.preventDefault();
                validateSponsorInput(currentElement);
                return;
            }
        }
        
        if (key === 'Enter') {
            event.preventDefault();
            
            // الانتقال للحقل التالي
            const currentRow = currentElement.closest('.bulk-debt-row');
            
            if (currentElement.classList.contains('bulk-debt-sponsor')) {
                // من حقل الراعي إلى حقل المبلغ
                const amountField = currentRow.querySelector('.bulk-debt-amount');
                if (amountField) amountField.focus();
            } else if (currentElement.classList.contains('bulk-debt-amount')) {
                // من حقل المبلغ إلى الصف التالي أو إضافة صف جديد
                const nextRow = currentRow.nextElementSibling;
                if (nextRow && nextRow.classList.contains('bulk-debt-row')) {
                    const nextSponsorField = nextRow.querySelector('.bulk-debt-sponsor');
                    if (nextSponsorField) nextSponsorField.focus();
                } else {
                    // إضافة صف جديد والانتقال إليه
                    addBulkDebtRow();
                    setTimeout(() => {
                        const newRow = currentRow.parentElement.lastElementChild;
                        const newSponsorField = newRow.querySelector('.bulk-debt-sponsor');
                        if (newSponsorField) newSponsorField.focus();
                    }, 50);
                }
            }
        }
    }
}

// إضافة صف دين جديد
function addBulkDebtRow() {
    const container = document.getElementById('bulkDebtsContainer');
    container.insertAdjacentHTML('beforeend', createBulkDebtRow());
}

// حذف صف دين
function removeBulkDebtRow(button) {
    const container = document.getElementById('bulkDebtsContainer');
    const rows = container.querySelectorAll('.bulk-debt-row');
    
    if (rows.length > 1) {
        button.closest('.bulk-debt-row').remove();
    } else {
        showError('يجب أن يبقى صف واحد على الأقل');
    }
}

// معالجة التنقل في نموذج الإدخال المجمع
function handleBulkDebtNavigation(event, element) {
    const key = event.key;
    const currentRow = element.closest('.bulk-debt-row');
    const container = document.getElementById('bulkDebtsContainer');
    const allRows = Array.from(container.querySelectorAll('.bulk-debt-row'));
    const currentRowIndex = allRows.indexOf(currentRow);
    
    // التنقل بالأسهم
    if (key === 'ArrowDown') {
        event.preventDefault();
        navigateToRow(currentRowIndex + 1, element.classList.contains('bulk-debt-sponsor') ? 'sponsor' : 'amount');
    } else if (key === 'ArrowUp') {
        event.preventDefault();
        navigateToRow(currentRowIndex - 1, element.classList.contains('bulk-debt-sponsor') ? 'sponsor' : 'amount');
    } else if (key === 'ArrowRight') {
        event.preventDefault();
        if (element.classList.contains('bulk-debt-sponsor')) {
            // من الراعي إلى المبلغ
            const amountField = currentRow.querySelector('.bulk-debt-amount');
            if (amountField) amountField.focus();
        }
    } else if (key === 'ArrowLeft') {
        event.preventDefault();
        if (element.classList.contains('bulk-debt-amount')) {
            // من المبلغ إلى الراعي
            const sponsorField = currentRow.querySelector('.bulk-debt-sponsor');
            if (sponsorField) sponsorField.focus();
        }
    }
    // التنقل بـ Enter
    else if (key === 'Enter') {
        event.preventDefault();
        
        if (element.classList.contains('bulk-debt-sponsor')) {
            // من الراعي إلى المبلغ في نفس الصف
            const amountField = currentRow.querySelector('.bulk-debt-amount');
            if (amountField) amountField.focus();
        } else if (element.classList.contains('bulk-debt-amount')) {
            // من المبلغ إلى صف جديد
            if (element.value.trim() !== '') {
                addBulkDebtRow();
                // التركيز على الراعي في الصف الجديد
                setTimeout(() => {
                    const newRows = container.querySelectorAll('.bulk-debt-row');
                    const lastRow = newRows[newRows.length - 1];
                    const sponsorField = lastRow.querySelector('.bulk-debt-sponsor');
                    if (sponsorField) sponsorField.focus();
                }, 100);
            }
        }
    }
}

// التنقل إلى صف محدد
function navigateToRow(targetRowIndex, fieldType) {
    const container = document.getElementById('bulkDebtsContainer');
    const allRows = Array.from(container.querySelectorAll('.bulk-debt-row'));
    
    if (targetRowIndex >= 0 && targetRowIndex < allRows.length) {
        const targetRow = allRows[targetRowIndex];
        const fieldClass = fieldType === 'sponsor' ? '.bulk-debt-sponsor' : '.bulk-debt-amount';
        const targetField = targetRow.querySelector(fieldClass);
        if (targetField) targetField.focus();
    }
}

// تحسين إضافة صف جديد مع التركيز
function addBulkDebtRowWithFocus() {
    addBulkDebtRow();
    setTimeout(() => {
        const container = document.getElementById('bulkDebtsContainer');
        const newRows = container.querySelectorAll('.bulk-debt-row');
        const lastRow = newRows[newRows.length - 1];
        const sponsorField = lastRow.querySelector('.bulk-debt-sponsor');
        if (sponsorField) sponsorField.focus();
    }, 100);
}

// حفظ الديون المجمعة
async function saveBulkDebts() {
    const form = document.getElementById('bulkDebtForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const branchId = document.getElementById('bulkDebtBranch').value;
    const date = document.getElementById('bulkDebtDate').value;
    const commonDescription = document.getElementById('bulkDebtDescription').value.trim();

    // جمع بيانات الديون من الصفوف
    const debtRows = document.querySelectorAll('.bulk-debt-row');
    const debtsData = [];

    for (let row of debtRows) {
        const sponsorInput = row.querySelector('.bulk-debt-sponsor');
        const amountInput = row.querySelector('.bulk-debt-amount');
        
        const sponsorId = sponsorInput.getAttribute('data-sponsor-id');
        
        if (sponsorId && amountInput.value) {
            const originalAmount = parseFloat(amountInput.value);
            const finalAmount = originalAmount * 1000; // ضرب في 1000
            debtsData.push({
                sponsor_id: parseInt(sponsorId),
                amount: finalAmount,
                description: commonDescription
            });
        }
    }

    if (debtsData.length === 0) {
        showError('يجب إدخال دين واحد على الأقل');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/debts/bulk`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                branch_id: parseInt(branchId),
                date: date,
                debts: debtsData
            })
        });

        const data = await response.json();

        if (data.success) {
            // حساب المجموع الإجمالي للمبالغ المحفوظة
            const totalAmount = debtsData.reduce((sum, debt) => sum + debt.amount, 0);
            showSuccess(`تم إضافة ${debtsData.length} دين بنجاح - المجموع: ${formatCurrency(totalAmount)}`);
            bootstrap.Modal.getInstance(document.getElementById('bulkDebtModal')).hide();
            loadDebts(); // إعادة تحميل قائمة الديون
        } else {
            showError(data.message || 'فشل في حفظ الديون');
        }
    } catch (error) {
        console.error('خطأ في حفظ الديون المجمعة:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}
