/*
  @license
	Rollup.js v4.47.1
	Thu, 21 Aug 2025 08:43:12 GMT - commit 21d5c5b74ec8c2cddef08a4122c3411e83f3d62f

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
