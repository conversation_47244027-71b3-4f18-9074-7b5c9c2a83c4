# Repo Overview

- Project: Sales Management System for Sellers (Django + DRF + Vue)
- Purpose: Manage daily entries, currency conversions (old↔new Rial based on USD), cargo (kilo) fees, final sales, and detailed reports with filtering.
- Key Entities:
  - Sellers, WorkDays, DailyEntries (repeat allowed per seller/day), DayRates (per day), DailyCalculations (snapshot aggregated per seller/day), FinalSales.
- Core Flows:
  1) Daily Entries per seller/day.
  2) Day Rates entry (USD→OLD, USD→NEW, kilo price) and Commit → snapshots (DailyCalculations) per seller/day.
  3) Final Sales entry (actual_sale) and compute profit/loss.
  4) Reports (daily/weekly/monthly/yearly) with filters (date range, seller, profit/loss).
- Precision: Use Decimal (numeric) with storage precision (money: 18,4; rates: 18,6). Round to 2 decimals on display only.

# Tech Stack
- Backend: Django + Django REST Framework
- Frontend: Vue 3 (to be scaffolded next)
- DB: SQLite for local dev (switchable to PostgreSQL later)

# Initial Tasks
1) Create Django project and `sales` app, configure DRF & CORS.
2) Implement models: Seller, WorkDay, DailyEntry, DayRates, DailyCalculation, FinalSale.
3) Serializers, ViewSets, and routing for CRUD + bulk endpoints.
4) Implement commit service to aggregate and snapshot calculations per seller/day.
5) Basic reporting endpoint (summary) with filters; CSV export later.

# Conventions
- Timezone: UTC in DB; display local as needed.
- Rounding: display only (2 decimals).
- Snapshots are immutable records for financial integrity.

# Notes
- This file helps assistants keep context about repository goals and constraints.