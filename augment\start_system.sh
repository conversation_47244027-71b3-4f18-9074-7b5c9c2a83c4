#!/bin/bash

echo ""
echo "========================================"
echo "    🚀 نظام المحاسبة للرعاة"
echo "    Sponsors Accounting System"
echo "========================================"
echo ""

echo "📋 التحقق من المتطلبات..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python غير مثبت. يرجى تثبيت Python أولاً."
    exit 1
fi

echo "✅ Python مثبت"
echo ""

echo "📦 تثبيت المكتبات المطلوبة..."
cd backend
pip3 install -r requirements.txt > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المكتبات"
    exit 1
fi

echo "✅ تم تثبيت المكتبات بنجاح"
echo ""

echo "🗄️ إعداد قاعدة البيانات..."
python3 add_sample_data.py > /dev/null 2>&1
echo "✅ تم إعداد قاعدة البيانات والبيانات التجريبية"
echo ""

echo "🌐 بدء تشغيل الخادم..."
echo ""
echo "========================================"
echo "    النظام جاهز للاستخدام!"
echo "    🌐 الرابط: http://localhost:5000"
echo "    📱 للإيقاف: اضغط Ctrl+C"
echo "========================================"
echo ""

python3 app.py
