<template>
  <div class="space-y-4">
    <h3 class="text-xl font-semibold">أسعار اليوم والاعتماد</h3>
    <div class="flex gap-3 items-center">
      <label>التاريخ:</label>
      <input class="border rounded px-2 py-1" type="date" v-model="date" />
      <button class="bg-indigo-600 text-white px-3 py-1 rounded disabled:opacity-50" @click="loadPreview" :disabled="!date">معاينة</button>
      <button class="bg-green-600 text-white px-3 py-1 rounded disabled:opacity-50" @click="commitCalc" :disabled="!date">اعتماد وحفظ</button>
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
      <div>
        <label class="block text-sm text-gray-600">USD→OLD</label>
        <input class="border rounded px-2 py-1 w-full" type="number" step="0.000001" v-model="usd_to_old" />
      </div>
      <div>
        <label class="block text-sm text-gray-600">USD→NEW</label>
        <input class="border rounded px-2 py-1 w-full" type="number" step="0.000001" v-model="usd_to_new" />
      </div>
      <div>
        <label class="block text-sm text-gray-600">سعر الكيلو</label>
        <input class="border rounded px-2 py-1 w-full" type="number" step="0.01" v-model="kilo_price" />
      </div>
    </div>
    <button class="bg-blue-600 text-white px-3 py-1 rounded" @click="saveRates" :disabled="!date">حفظ الأسعار</button>

    <div class="overflow-x-auto" v-if="preview.length">
      <table class="min-w-full bg-white rounded shadow mt-3">
        <thead class="bg-gray-100">
          <tr>
            <th class="text-right p-2">البياع</th>
            <th class="text-right p-2">مجموع قديم</th>
            <th class="text-right p-2">مجموع كيلوا</th>
            <th class="text-right p-2">بعد التحويل</th>
            <th class="text-right p-2">الخرج</th>
            <th class="text-right p-2">الإجمالي</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="p in preview" :key="p.seller" class="border-t">
            <td class="p-2">{{ p.seller_name }}</td>
            <td class="p-2">{{ fmt(p.amount_old_sum) }}</td>
            <td class="p-2">{{ fmt(p.kilos_sum) }}</td>
            <td class="p-2">{{ fmt(p.amount_new) }}</td>
            <td class="p-2">{{ fmt(p.cargo_amount) }}</td>
            <td class="p-2">{{ fmt(p.total_after_cargo) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
import axios from 'axios'
import { ref } from 'vue'

const date = ref('')
const usd_to_old = ref('')
const usd_to_new = ref('')
const kilo_price = ref('')
const preview = ref([])

function fmt(x){
  const n = Number(x||0)
  return n.toLocaleString('ar', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

async function saveRates(){
  await axios.post('/api/work-days/', { work_date: date.value }).catch(()=>{})
  const wd = (await axios.get('/api/work-days/')).data.find(w=>w.work_date===date.value)
  const list = (await axios.get('/api/day-rates/')).data
  const existing = list.find(r=>r.work_day===wd.id)
  if(existing){
    await axios.put(`/api/day-rates/${existing.id}/`, {
      work_day: wd.id, usd_to_old: usd_to_old.value, usd_to_new: usd_to_new.value, kilo_price: kilo_price.value, locked: false
    })
  }else{
    await axios.post('/api/day-rates/', {
      work_day: wd.id, usd_to_old: usd_to_old.value, usd_to_new: usd_to_new.value, kilo_price: kilo_price.value
    })
  }
  alert('تم حفظ الأسعار')
}

async function loadPreview(){
  preview.value = (await axios.post(`/api/calculations/commit?date=${date.value}`)).data
}

async function commitCalc(){
  await loadPreview()
  alert('تم الاعتماد')
}
</script>