// نظام المحاسبة للرعاة - التقارير المحسنة مع دعم الفروع
// Enhanced Reports functionality for Accounting System with Branch Support

// متغيرات التقارير
let currentReportData = null;
let currentReportType = null;
let reportBranches = [];
let reportSponsors = [];

// إعدادات التقارير المحسنة
const reportSettings = {
    itemsPerPage: 10,
    sortColumn: null,
    sortDirection: 'asc',
    searchTerm: '',
    filters: {
        dateFrom: null,
        dateTo: null,
        branchId: null,
        sponsorId: null,
        isActive: null
    }
};

// تحميل بيانات الفروع والرعاة للتقارير
async function loadReportData() {
    try {
        const [branchesResponse, sponsorsResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/branches`),
            fetch(`${API_BASE_URL}/sponsors`)
        ]);

        const branchesData = await branchesResponse.json();
        const sponsorsData = await sponsorsResponse.json();

        if (branchesData.success) {
            reportBranches = branchesData.data;
        }
        if (sponsorsData.success) {
            reportSponsors = sponsorsData.data;
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات التقارير:', error);
    }
}

// إنشاء فلاتر التقارير المتقدمة
function createReportFilters() {
    return `
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقرير
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" class="form-control" id="filterDateFrom" 
                               onchange="updateReportFilters()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" class="form-control" id="filterDateTo" 
                               onchange="updateReportFilters()">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الفرع:</label>
                        <select class="form-select" id="filterBranch" onchange="updateReportFilters()">
                            <option value="">جميع الفروع</option>
                            ${reportBranches.filter(b => b.is_active).map(branch => 
                                `<option value="${branch.id}">${branch.name} (${branch.code})</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الراعي:</label>
                        <select class="form-select" id="filterSponsor" onchange="updateReportFilters()">
                            <option value="">جميع الرعاة</option>
                            ${reportSponsors.map(sponsor => 
                                `<option value="${sponsor.id}">${sponsor.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <button class="btn btn-primary btn-sm me-2" onclick="applyReportFilters()">
                            <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="clearReportFilters()">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// تحديث فلاتر التقرير
function updateReportFilters() {
    reportSettings.filters.dateFrom = document.getElementById('filterDateFrom')?.value || null;
    reportSettings.filters.dateTo = document.getElementById('filterDateTo')?.value || null;
    reportSettings.filters.branchId = document.getElementById('filterBranch')?.value || null;
    reportSettings.filters.sponsorId = document.getElementById('filterSponsor')?.value || null;
}

// تطبيق فلاتر التقرير
function applyReportFilters() {
    // حفظ القيم الحالية للفلاتر قبل التحديث
    const currentFilters = {
        dateFrom: document.getElementById('filterDateFrom')?.value || '',
        dateTo: document.getElementById('filterDateTo')?.value || '',
        branchId: document.getElementById('filterBranch')?.value || '',
        sponsorId: document.getElementById('filterSponsor')?.value || ''
    };
    
    updateReportFilters();
    
    if (currentReportType) {
        switch (currentReportType) {
            case 'summary':
                loadSummaryReport().then(() => {
                    // استعادة قيم الفلاتر بعد تحميل التقرير
                    restoreFilterValues(currentFilters);
                });
                break;
            case 'daily':
                loadDailyReport().then(() => {
                    restoreFilterValues(currentFilters);
                });
                break;
            case 'monthly':
                loadMonthlyReport().then(() => {
                    restoreFilterValues(currentFilters);
                });
                break;
            case 'sponsor':
                loadSponsorReport().then(() => {
                    restoreFilterValues(currentFilters);
                });
                break;
            default:
                console.log('نوع تقرير غير معروف:', currentReportType);
        }
    }
}

// استعادة قيم الفلاتر
function restoreFilterValues(filters) {
    setTimeout(() => {
        if (document.getElementById('filterDateFrom')) {
            document.getElementById('filterDateFrom').value = filters.dateFrom;
        }
        if (document.getElementById('filterDateTo')) {
            document.getElementById('filterDateTo').value = filters.dateTo;
        }
        if (document.getElementById('filterBranch')) {
            document.getElementById('filterBranch').value = filters.branchId;
        }
        if (document.getElementById('filterSponsor')) {
            document.getElementById('filterSponsor').value = filters.sponsorId;
        }
    }, 100);
}

// مسح فلاتر التقرير
function clearReportFilters() {
    document.getElementById('filterDateFrom').value = '';
    document.getElementById('filterDateTo').value = '';
    document.getElementById('filterBranch').value = '';
    document.getElementById('filterSponsor').value = '';
    
    reportSettings.filters = {
        dateFrom: null,
        dateTo: null,
        branchId: null,
        sponsorId: null,
        isActive: null
    };
    
    applyReportFilters();
}

// تحميل الملخص الإجمالي مع الفلاتر
async function loadSummaryReport() {
    try {
        await loadReportData();
        
        let url = `${API_BASE_URL}/reports/summary`;
        const params = new URLSearchParams();
        
        if (reportSettings.filters.dateFrom) {
            params.append('from_date', reportSettings.filters.dateFrom);
        }
        if (reportSettings.filters.dateTo) {
            params.append('to_date', reportSettings.filters.dateTo);
        }
        if (reportSettings.filters.branchId) {
            params.append('branch_id', reportSettings.filters.branchId);
        }
        if (reportSettings.filters.sponsorId) {
            params.append('sponsor_id', reportSettings.filters.sponsorId);
        }
        
        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            displayEnhancedSummaryReport(data.data);
        } else {
            showError('فشل في تحميل الملخص الإجمالي: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل الملخص الإجمالي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض الملخص الإجمالي المحسن
function displayEnhancedSummaryReport(data) {
    currentReportData = data;
    currentReportType = 'summary';
    
    let html = `
        ${createReportFilters()}
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الملخص الإجمالي المحسن
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportReport()">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- معلومات الفلتر المطبق -->
                ${data.filter && (data.filter.from_date || data.filter.to_date || data.filter.branch_id || data.filter.sponsor_id) ? `
                <div class="alert alert-info mb-3">
                    <h6><i class="fas fa-filter me-2"></i>الفلتر المطبق:</h6>
                    ${data.filter.from_date ? `<span class="badge bg-primary me-2">من: ${data.filter.from_date}</span>` : ''}
                    ${data.filter.to_date ? `<span class="badge bg-primary me-2">إلى: ${data.filter.to_date}</span>` : ''}
                    ${data.filter.branch_name ? `<span class="badge bg-success me-2">الفرع: ${data.filter.branch_name}</span>` : ''}
                    ${data.filter.sponsor_name ? `<span class="badge bg-warning me-2">الراعي: ${data.filter.sponsor_name}</span>` : ''}
                </div>
                ` : ''}
                
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card bg-primary text-white p-3 rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users fa-2x me-3"></i>
                                <div>
                                    <h4>${data.totals?.sponsors_count || 0}</h4>
                                    <small>عدد الرعاة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card bg-success text-white p-3 rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-building fa-2x me-3"></i>
                                <div>
                                    <h4>${data.totals?.branches_count || 0}</h4>
                                    <small>عدد الفروع</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card bg-danger text-white p-3 rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-invoice-dollar fa-2x me-3"></i>
                                <div>
                                    <h4>${formatCurrency(data.totals?.total_debts || 0)}</h4>
                                    <small>إجمالي الديون</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card bg-info text-white p-3 rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-money-bill-transfer fa-2x me-3"></i>
                                <div>
                                    <h4>${formatCurrency(data.totals?.total_remittances || 0)}</h4>
                                    <small>إجمالي الحوالات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card bg-warning text-white p-3 rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-balance-scale fa-2x me-3"></i>
                                <div>
                                    <h4>${formatCurrency(data.totals?.total_balance || 0)}</h4>
                                    <small>الرصيد الصافي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card bg-secondary text-white p-3 rounded">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-line fa-2x me-3"></i>
                                <div>
                                    <h4>${reportBranches.filter(b => b.is_active).length}</h4>
                                    <small>الفروع النشطة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- جدول الرعاة -->
                ${data.sponsors && data.sponsors.length > 0 ? `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-users me-2"></i>
                            بيانات الرعاة
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-primary">
                                    <tr>
                                        <th>الراعي</th>
                                        <th>الهاتف</th>
                                        <th>إجمالي الديون</th>
                                        <th>إجمالي الحوالات</th>
                                        <th>الرصيد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.sponsors.map(sponsor => `
                                        <tr>
                                            <td><strong>${sponsor.name}</strong></td>
                                            <td>${sponsor.phone || '-'}</td>
                                            <td class="text-danger">${formatCurrency(sponsor.total_debts || 0)}</td>
                                            <td class="text-success">${formatCurrency(sponsor.total_remittances || 0)}</td>
                                            <td class="text-${(sponsor.balance || 0) >= 0 ? 'success' : 'danger'}">
                                                ${formatCurrency(sponsor.balance || 0)}
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                ` : ''}
                
                <!-- جدول الفروع -->
                ${data.branches && data.branches.length > 0 ? `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-building me-2"></i>
                            بيانات الفروع
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-primary">
                                    <tr>
                                        <th>الفرع</th>
                                        <th>الرمز</th>
                                        <th>عدد الديون</th>
                                        <th>إجمالي الديون</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.branches.map(branch => `
                                        <tr>
                                            <td><strong>${branch.name}</strong></td>
                                            <td><code>${branch.code}</code></td>
                                            <td class="text-center">${branch.debts_count || 0}</td>
                                            <td class="text-danger">${formatCurrency(branch.total_debts || 0)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    document.getElementById('reports').innerHTML = html;
}

// تحديث دالة تحميل التقارير في app.js
async function loadReports() {
    try {
        await loadReportData();
        displayReportsPage();
    } catch (error) {
        console.error('خطأ في تحميل التقارير:', error);
        showError('خطأ في تحميل التقارير');
    }
}
