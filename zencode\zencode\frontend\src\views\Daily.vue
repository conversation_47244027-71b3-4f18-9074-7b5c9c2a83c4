<template>
  <div class="space-y-4">
    <h3 class="text-xl font-semibold">الإدخال اليومي</h3>
    <div class="flex gap-3 items-center">
      <label>التاريخ:</label>
      <input class="border rounded px-2 py-1" type="date" v-model="date" />
      <button class="bg-blue-600 text-white px-3 py-1 rounded" @click="addRow">إضافة صف</button>
      <button class="bg-green-600 text-white px-3 py-1 rounded disabled:opacity-50" @click="saveBulk" :disabled="!rows.length || !date">حفظ</button>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white rounded shadow">
        <thead class="bg-gray-100">
          <tr>
            <th class="text-right p-2">البياع</th>
            <th class="text-right p-2">مبلغ (قديم)</th>
            <th class="text-right p-2">كيلو</th>
            <th class="text-right p-2">ملاحظة</th>
            <th class="text-right p-2"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(r,i) in rows" :key="i" class="border-t">
            <td class="p-2">
              <select class="border rounded px-2 py-1 w-full" v-model.number="r.seller">
                <option :value="s.id" v-for="s in sellers" :key="s.id">{{ s.name }}</option>
              </select>
            </td>
            <td class="p-2"><input class="border rounded px-2 py-1 w-full" type="number" step="0.01" v-model="r.amount_old" /></td>
            <td class="p-2"><input class="border rounded px-2 py-1 w-full" type="number" step="0.01" v-model="r.kilos" /></td>
            <td class="p-2"><input class="border rounded px-2 py-1 w-full" v-model="r.note" /></td>
            <td class="p-2 text-center"><button class="text-red-600" @click="rows.splice(i,1)">حذف</button></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
import axios from 'axios'
import { ref, onMounted } from 'vue'

const date = ref('')
const rows = ref([])
const sellers = ref([])

function addRow(){
  rows.value.push({ seller: sellers.value[0]?.id || null, amount_old: '', kilos: '', note: '' })
}

async function loadSellers(){
  const res = await axios.get('/api/sellers/')
  sellers.value = res.data
}

async function ensureWorkDay(){
  // create WorkDay if not exists
  await axios.post('/api/work-days/', { work_date: date.value }).catch(()=>{})
}

async function saveBulk(){
  if(!date.value) return
  await ensureWorkDay()
  const wd = (await axios.get('/api/work-days/')).data.find(w=>w.work_date===date.value)
  const payload = { work_day: wd.id, entries: rows.value.map(r=>({
    seller: r.seller, amount_old: r.amount_old, kilos: r.kilos, note: r.note
  })) }
  await axios.post('/api/daily-entries/bulk/', payload)
  rows.value = []
  alert('تم الحفظ')
}

onMounted(loadSellers)
</script>