# -*- coding: utf-8 -*-
"""
سكريبت إصلاح قاعدة البيانات
Database Fix Script
"""

import os
import sys
from flask import Flask
from models import db, Branch, Sponsor, Debt, Remittance
from datetime import datetime

def create_app():
    """إنشاء تطبيق Flask"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'accounting-system-secret-key-2024'
    
    # إعداد قاعدة البيانات
    basedir = os.path.abspath(os.path.dirname(__file__))
    database_path = os.path.join(basedir, '..', 'database', 'accounting.db')
    os.makedirs(os.path.dirname(database_path), exist_ok=True)
    
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    return app

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("🔧 بدء إصلاح قاعدة البيانات...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # حذف الجداول الموجودة
            print("🗑️ حذف الجداول القديمة...")
            db.drop_all()
            
            # إنشاء جداول جديدة
            print("🏗️ إنشاء جداول جديدة...")
            db.create_all()
            
            # إضافة فروع تجريبية
            print("🏢 إضافة فروع تجريبية...")
            branch1 = Branch(
                name="فرع الغيظة",
                code="BR001",
                description="الفرع الرئيسي في الغيظة",
                address="الغيظة، صنعاء، اليمن",
                phone="777123456",
                manager_name="أحمد محمد",
                is_active=True
            )
            
            branch2 = Branch(
                name="فرع شحن",
                code="BR002", 
                description="فرع شحن الفرعي",
                address="شحن، صنعاء، اليمن",
                phone="777654321",
                manager_name="محمد أحمد",
                is_active=True
            )
            
            db.session.add(branch1)
            db.session.add(branch2)
            db.session.commit()
            
            # إضافة رعاة تجريبيين
            print("👥 إضافة رعاة تجريبيين...")
            sponsors_data = [
                {"name": "علي محمد الأحمر", "phone": "777111222", "address": "صنعاء، اليمن"},
                {"name": "فاطمة أحمد السالم", "phone": "777333444", "address": "تعز، اليمن"},
                {"name": "محمد عبدالله الزبيري", "phone": "777555666", "address": "الحديدة، اليمن"},
                {"name": "خديجة سالم المطري", "phone": "777777888", "address": "إب، اليمن"},
                {"name": "عبدالرحمن يحيى الحوثي", "phone": "777999000", "address": "صعدة، اليمن"}
            ]
            
            sponsors = []
            for sponsor_data in sponsors_data:
                sponsor = Sponsor(**sponsor_data)
                db.session.add(sponsor)
                sponsors.append(sponsor)
            
            db.session.commit()
            
            # إضافة ديون تجريبية
            print("💰 إضافة ديون تجريبية...")
            debts_data = [
                {"sponsor": sponsors[0], "branch": branch1, "amount": 50000, "description": "دين شهر يناير"},
                {"sponsor": sponsors[1], "branch": branch1, "amount": 75000, "description": "دين شهر فبراير"},
                {"sponsor": sponsors[2], "branch": branch2, "amount": 30000, "description": "دين شهر مارس"},
                {"sponsor": sponsors[3], "branch": branch1, "amount": 100000, "description": "دين شهر أبريل"},
                {"sponsor": sponsors[4], "branch": branch2, "amount": 25000, "description": "دين شهر مايو"}
            ]
            
            for debt_data in debts_data:
                debt = Debt(
                    sponsor_id=debt_data["sponsor"].id,
                    branch_id=debt_data["branch"].id,
                    amount=debt_data["amount"],
                    description=debt_data["description"],
                    date=datetime.now().date()
                )
                db.session.add(debt)
            
            db.session.commit()
            
            # إضافة حوالات تجريبية
            print("💸 إضافة حوالات تجريبية...")
            remittances_data = [
                {"sponsor": sponsors[0], "amount": 20000, "source": "بنك الكريمي"},
                {"sponsor": sponsors[1], "amount": 30000, "source": "حوالة نقدية"},
                {"sponsor": sponsors[2], "amount": 15000, "source": "بنك سبأ"},
                {"sponsor": sponsors[3], "amount": 40000, "source": "بنك اليمن الدولي"},
                {"sponsor": sponsors[4], "amount": 10000, "source": "حوالة نقدية"}
            ]
            
            for remittance_data in remittances_data:
                remittance = Remittance(
                    sponsor_id=remittance_data["sponsor"].id,
                    amount=remittance_data["amount"],
                    source=remittance_data["source"],
                    date=datetime.now().date()
                )
                db.session.add(remittance)
            
            db.session.commit()
            
            print("✅ تم إصلاح قاعدة البيانات بنجاح!")
            print(f"📊 تم إنشاء:")
            print(f"   - {len(Branch.query.all())} فرع")
            print(f"   - {len(Sponsor.query.all())} راعي")
            print(f"   - {len(Debt.query.all())} دين")
            print(f"   - {len(Remittance.query.all())} حوالة")
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح قاعدة البيانات: {str(e)}")
            db.session.rollback()
            return False
    
    return True

if __name__ == "__main__":
    success = fix_database()
    if success:
        print("\n🎉 قاعدة البيانات جاهزة للاستخدام!")
    else:
        print("\n💥 فشل في إصلاح قاعدة البيانات!")
        sys.exit(1)
