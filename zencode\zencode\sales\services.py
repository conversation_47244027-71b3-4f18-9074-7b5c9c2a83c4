from decimal import Decimal, ROUND_HALF_UP
from django.db.models import Sum
from .models import DailyEntry, DayRates, DailyCalculation, WorkDay

TWOPLACES = Decimal('0.01')


def round2(value: Decimal) -> Decimal:
    # Round for display/export only if needed elsewhere
    return (value or Decimal('0')).quantize(TWOPLACES, rounding=ROUND_HALF_UP)


def commit_calculations(work_day: WorkDay):
    rates = DayRates.objects.get(work_day=work_day)
    grouped = (
        DailyEntry.objects
        .filter(work_day=work_day)
        .values('seller_id')
        .annotate(amount_old_sum=Sum('amount_old'), kilos_sum=Sum('kilos'))
    )
    results = []
    for g in grouped:
        amount_old_sum = g['amount_old_sum'] or Decimal('0')
        kilos_sum = g['kilos_sum'] or Decimal('0')
        # Core formulas
        amount_new = amount_old_sum * (rates.usd_to_new / rates.usd_to_old)
        cargo_amount = kilos_sum * rates.kilo_price
        total_after_cargo = amount_new + cargo_amount
        obj, _ = DailyCalculation.objects.update_or_create(
            work_day=work_day, seller_id=g['seller_id'],
            defaults=dict(
                amount_old_sum=amount_old_sum,
                kilos_sum=kilos_sum,
                usd_to_old=rates.usd_to_old,
                usd_to_new=rates.usd_to_new,
                kilo_price=rates.kilo_price,
                amount_new=amount_new,
                cargo_amount=cargo_amount,
                total_after_cargo=total_after_cargo,
            )
        )
        results.append(obj)
    return results