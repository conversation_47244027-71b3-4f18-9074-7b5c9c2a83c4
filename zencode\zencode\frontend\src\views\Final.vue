<template>
  <div class="space-y-4">
    <h3 class="text-xl font-semibold">المبيعات النهائية</h3>
    <div class="flex gap-3 items-center">
      <label>التاريخ:</label>
      <input class="border rounded px-2 py-1" type="date" v-model="date" />
      <button class="bg-indigo-600 text-white px-3 py-1 rounded disabled:opacity-50" @click="loadCalcs" :disabled="!date">تحميل</button>
      <button class="bg-green-600 text-white px-3 py-1 rounded disabled:opacity-50" @click="save" :disabled="!items.length">حفظ</button>
    </div>
    <div class="overflow-x-auto" v-if="items.length">
      <table class="min-w-full bg-white rounded shadow">
        <thead class="bg-gray-100">
          <tr>
            <th class="text-right p-2">البياع</th>
            <th class="text-right p-2">الإجمالي المحسوب</th>
            <th class="text-right p-2">سعر البيع الفعلي</th>
            <th class="text-right p-2">الربح/الخسارة</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="it in items" :key="it.seller" class="border-t">
            <td class="p-2">{{ it.seller_name }}</td>
            <td class="p-2">{{ fmt(it.total_after_cargo) }}</td>
            <td class="p-2"><input class="border rounded px-2 py-1 w-full" type="number" step="0.01" v-model="it.actual_sale" /></td>
            <td class="p-2" :class="Number(it.actual_sale||0) - Number(it.total_after_cargo||0) >= 0 ? 'text-green-600' : 'text-red-600'">
              {{ fmt(Number(it.actual_sale||0) - Number(it.total_after_cargo||0)) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
import axios from 'axios'
import { ref } from 'vue'

const date = ref('')
const items = ref([])

function fmt(x){
  const n = Number(x||0)
  return n.toLocaleString('ar', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

async function loadCalcs(){
  const data = (await axios.get('/api/calculations/')).data
  const calcs = data.filter(c=>c.work_day && c)
  items.value = calcs.map(c=>({
    work_day: c.work_day,
    seller: c.seller,
    seller_name: c.seller_name,
    calc_total: c.total_after_cargo,
    total_after_cargo: c.total_after_cargo,
    actual_sale: ''
  }))
}

async function save(){
  await axios.post('/api/final-sales/bulk/', items.value.map(x=>({
    work_day: x.work_day,
    seller: x.seller,
    calc_total: x.calc_total,
    actual_sale: x.actual_sale
  })))
  alert('تم الحفظ')
}
</script>