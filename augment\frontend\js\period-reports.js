// نظام المحاسبة للرعاة - تقارير الفترات الزمنية
// Period Reports functionality for Accounting System

// تنسيق التاريخ للعرض
function formatDateForDisplay(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    };
    return date.toLocaleDateString('ar-SA', options);
}

// عرض تقرير الحوالات للفترة المحددة
function displayRemittancesPeriodReport(data) {
    currentReportData = data;
    currentReportType = 'remittances-period';
    
    const fromDate = formatDate(data.period.from_date);
    const toDate = formatDate(data.period.to_date);
    
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-transfer me-2 text-success"></i>
                    تقرير الحوالات للفترة من ${fromDate} إلى ${toDate}
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="printEnhancedReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportToPDF('remittances-period')">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stat-card bg-success text-white h-100">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number mb-2">${formatCurrency(data.totals.total_amount)}</h3>
                                <p class="stat-label mb-0">إجمالي الحوالات</p>
                                <small class="opacity-75">للفترة المحددة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card bg-info text-white h-100">
                            <div class="stat-icon">
                                <i class="fas fa-list-ol fa-2x"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number mb-2">${data.totals.total_count}</h3>
                                <p class="stat-label mb-0">عدد الحوالات</p>
                                <small class="opacity-75">معاملة مالية</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card bg-primary text-white h-100">
                            <div class="stat-icon">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number mb-2">${data.totals.sponsors_count}</h3>
                                <p class="stat-label mb-0">عدد الرعاة</p>
                                <small class="opacity-75">راعي نشط</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-card bg-warning text-white h-100">
                            <div class="stat-icon">
                                <i class="fas fa-building-columns fa-2x"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number mb-2">${data.totals.sources_count}</h3>
                                <p class="stat-label mb-0">مصادر الحوالات</p>
                                <small class="opacity-75">مصدر مختلف</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">توزيع الحوالات حسب الراعي</h6>
                            <div class="chart-wrapper">
                                <canvas id="remittancesSponsorsChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">توزيع الحوالات حسب المصدر</h6>
                            <div class="chart-wrapper">
                                <canvas id="remittancesSourcesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الرعاة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #198754 0%, #20c997 100%);">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-users me-2"></i>
                                    ملخص الحوالات حسب الراعي
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead class="table-primary">
                                            <tr>
                                                <th class="fw-bold"><i class="fas fa-user me-1"></i> الراعي</th>
                                                <th class="text-center fw-bold"><i class="fas fa-hashtag me-1"></i> عدد الحوالات</th>
                                                <th class="text-end fw-bold"><i class="fas fa-money-bill me-1"></i> إجمالي المبلغ</th>
                                                <th class="text-end fw-bold"><i class="fas fa-calculator me-1"></i> متوسط الحوالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
    `;
    
    // عرض ملخص الرعاة
    data.sponsors_summary.forEach((sponsor, index) => {
        const average = sponsor.total_amount / sponsor.remittances_count;
        const percentage = (sponsor.total_amount / data.totals.total_amount * 100).toFixed(1);
        html += `
            <tr class="table-row-hover">
                <td>
                    <div class="d-flex align-items-center">
                        <div class="sponsor-avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 0.8rem;">
                            ${sponsor.sponsor_name.charAt(0)}
                        </div>
                        <strong class="text-dark">${sponsor.sponsor_name}</strong>
                    </div>
                </td>
                <td class="text-center">
                    <span class="badge bg-info rounded-pill">${sponsor.remittances_count}</span>
                </td>
                <td class="text-end">
                    <div class="text-success text-currency fw-bold">${formatCurrency(sponsor.total_amount)}</div>
                    <small class="text-muted">${percentage}% من الإجمالي</small>
                </td>
                <td class="text-end">
                    <span class="text-info text-currency fw-bold">${formatCurrency(average)}</span>
                </td>
            </tr>
        `;
    });
    
    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص المصادر -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-building-columns me-2"></i>
                                    ملخص الحوالات حسب المصدر
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>مصدر الحوالة</th>
                                                <th>عدد الحوالات</th>
                                                <th>إجمالي المبلغ</th>
                                                <th>النسبة المئوية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
    `;
    
    // عرض ملخص المصادر
    data.sources_summary.forEach(source => {
        const percentage = (source.total_amount / data.totals.total_amount * 100).toFixed(1);
        html += `
            <tr>
                <td><strong>${source.source}</strong></td>
                <td class="text-center">${source.remittances_count}</td>
                <td class="text-success text-currency">${formatCurrency(source.total_amount)}</td>
                <td class="text-primary">${percentage}%</td>
            </tr>
        `;
    });
    
    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الحوالات -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    تفاصيل جميع الحوالات (${data.remittances.length})
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>الراعي</th>
                                                <th>المبلغ</th>
                                                <th>المصدر</th>
                                            </tr>
                                        </thead>
                                        <tbody>
    `;
    
    // عرض تفاصيل الحوالات
    data.remittances.forEach(remittance => {
        html += `
            <tr>
                <td>${formatDate(remittance.date)}</td>
                <td>${remittance.sponsor_name}</td>
                <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                <td>${remittance.source || 'غير محدد'}</td>
            </tr>
        `;
    });
    
    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('report-content').innerHTML = html;
    
    // إنشاء الرسوم البيانية
    setTimeout(() => {
        createRemittancesChartsForPeriod(data);
    }, 100);
}

// إنشاء الرسوم البيانية لتقرير الحوالات
function createRemittancesChartsForPeriod(data) {
    // رسم بياني للرعاة
    const sponsorsCtx = document.getElementById('remittancesSponsorsChart');
    if (sponsorsCtx) {
        setupCanvasSize(sponsorsCtx, 'sponsors');
        
        const sponsorsLabels = data.sponsors_summary.map(s => s.sponsor_name);
        const sponsorsData = data.sponsors_summary.map(s => s.total_amount);
        
        new Chart(sponsorsCtx, {
            type: 'doughnut',
            data: {
                labels: sponsorsLabels,
                datasets: [{
                    data: sponsorsData,
                    backgroundColor: [
                        '#198754', '#20c997', '#0dcaf0', '#17a2b8',
                        '#28a745', '#007bff', '#6f42c1', '#fd7e14',
                        '#ffc107', '#e83e8c', '#6c757d', '#495057'
                    ],
                    borderWidth: 3,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 5,
                    hoverBorderColor: '#ffffff',
                    hoverOffset: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 1.5,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: 'Noto Sans Arabic',
                                size: 12,
                                weight: '600'
                            },
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#198754',
                        borderWidth: 2,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label}: ${formatCurrency(value)} (${percentage}%)`;
                            }
                        },
                        titleFont: { family: 'Noto Sans Arabic', weight: 'bold' },
                        bodyFont: { family: 'Noto Sans Arabic' }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1500,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
    
    // رسم بياني للمصادر
    const sourcesCtx = document.getElementById('remittancesSourcesChart');
    if (sourcesCtx) {
        setupCanvasSize(sourcesCtx, 'sources');
        
        const sourcesLabels = data.sources_summary.map(s => s.source);
        const sourcesData = data.sources_summary.map(s => s.total_amount);
        
        new Chart(sourcesCtx, {
            type: 'bar',
            data: {
                labels: sourcesLabels,
                datasets: [{
                    label: 'إجمالي المبلغ',
                    data: sourcesData,
                    backgroundColor: sourcesData.map((_, index) => {
                        const colors = ['#198754', '#20c997', '#0dcaf0', '#17a2b8', '#28a745'];
                        return colors[index % colors.length];
                    }),
                    borderColor: sourcesData.map((_, index) => {
                        const colors = ['#157347', '#1aa085', '#0bb5d6', '#138496', '#1e7e34'];
                        return colors[index % colors.length];
                    }),
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: sourcesData.map((_, index) => {
                        const colors = ['#20c997', '#198754', '#17a2b8', '#0dcaf0', '#007bff'];
                        return colors[index % colors.length];
                    }),
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 2,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#198754',
                        borderWidth: 2,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                            }
                        },
                        titleFont: { family: 'Noto Sans Arabic', weight: 'bold' },
                        bodyFont: { family: 'Noto Sans Arabic' }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            font: {
                                family: 'Noto Sans Arabic',
                                size: 11,
                                weight: '600'
                            },
                            color: '#495057'
                        },
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            },
                            font: {
                                family: 'Noto Sans Arabic',
                                size: 11
                            },
                            color: '#495057'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            borderDash: [5, 5]
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
}

// عرض تقرير الديون للفترة المحددة
function displayDebtsPeriodReport(data) {
    currentReportData = data;
    currentReportType = 'debts-period';

    const fromDate = formatDate(data.period.from_date);
    const toDate = formatDate(data.period.to_date);

    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice-dollar me-2 text-danger"></i>
                    تقرير الديون للفترة من ${fromDate} إلى ${toDate}
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="printEnhancedReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportToPDF('debts-period')">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stat-card bg-danger text-white">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_amount)}</h3>
                                <p class="stat-label">إجمالي الديون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card bg-info text-white">
                            <div class="stat-icon">
                                <i class="fas fa-list-ol"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${data.totals.total_count}</h3>
                                <p class="stat-label">عدد الديون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card bg-primary text-white">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${data.totals.sponsors_count}</h3>
                                <p class="stat-label">عدد الرعاة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">توزيع الديون حسب الراعي</h6>
                            <div class="chart-wrapper">
                                <canvas id="debtsSponsorsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الرعاة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    ملخص الديون حسب الراعي
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>الراعي</th>
                                                <th>عدد الديون</th>
                                                <th>إجمالي المبلغ</th>
                                                <th>متوسط الدين</th>
                                                <th>النسبة المئوية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
    `;

    // عرض ملخص الرعاة
    data.sponsors_summary.forEach(sponsor => {
        const average = sponsor.total_amount / sponsor.debts_count;
        const percentage = (sponsor.total_amount / data.totals.total_amount * 100).toFixed(1);
        html += `
            <tr>
                <td><strong>${sponsor.sponsor_name}</strong></td>
                <td class="text-center">${sponsor.debts_count}</td>
                <td class="text-danger text-currency">${formatCurrency(sponsor.total_amount)}</td>
                <td class="text-info text-currency">${formatCurrency(average)}</td>
                <td class="text-primary">${percentage}%</td>
            </tr>
        `;
    });

    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الديون -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    تفاصيل جميع الديون (${data.debts.length})
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>الراعي</th>
                                                <th>المبلغ</th>
                                                <th>الوصف</th>
                                            </tr>
                                        </thead>
                                        <tbody>
    `;

    // عرض تفاصيل الديون
    data.debts.forEach(debt => {
        html += `
            <tr>
                <td>${formatDate(debt.date)}</td>
                <td>${debt.sponsor_name}</td>
                <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                <td>${debt.description || 'غير محدد'}</td>
            </tr>
        `;
    });

    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('report-content').innerHTML = html;

    // إنشاء الرسم البياني
    setTimeout(() => {
        createDebtsChartForPeriod(data);
    }, 100);
}

// إنشاء الرسم البياني لتقرير الديون
function createDebtsChartForPeriod(data) {
    const ctx = document.getElementById('debtsSponsorsChart');
    if (!ctx) return;

    setupCanvasSize(ctx, 'sponsors');

    const labels = data.sponsors_summary.map(s => s.sponsor_name);
    const chartData = data.sponsors_summary.map(s => s.total_amount);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'إجمالي الديون',
                data: chartData,
                backgroundColor: chartData.map((_, index) => {
                    const colors = ['#dc3545', '#fd7e14', '#e83e8c', '#6f42c1', '#495057'];
                    return colors[index % colors.length];
                }),
                borderColor: chartData.map((_, index) => {
                    const colors = ['#bb2d3b', '#e8681b', '#d63384', '#59359a', '#373a3c'];
                    return colors[index % colors.length];
                }),
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
                hoverBackgroundColor: chartData.map((_, index) => {
                    const colors = ['#fd7e14', '#dc3545', '#6f42c1', '#e83e8c', '#6c757d'];
                    return colors[index % colors.length];
                }),
                hoverBorderWidth: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 2.5,
            plugins: {
                legend: { display: false },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#dc3545',
                    borderWidth: 2,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    },
                    titleFont: { family: 'Noto Sans Arabic', weight: 'bold' },
                    bodyFont: { family: 'Noto Sans Arabic' }
                }
            },
            scales: {
                x: {
                    ticks: {
                        maxRotation: 45,
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 11,
                            weight: '600'
                        },
                        color: '#495057'
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        },
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 11
                        },
                        color: '#495057'
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        borderDash: [5, 5]
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// معالجة ما قبل الطباعة
function handleBeforePrint() {
    if (currentReportData && currentReportType === 'balance-period') {
        window.savedReportData = currentReportData;
    }
}

// معالجة ما بعد الطباعة - إعادة إنشاء الرسم البياني
function handleAfterPrint() {
    if (window.savedReportData && currentReportType === 'balance-period') {
        setTimeout(() => {
            createBalanceChartForPeriod(window.savedReportData);
        }, 200);
    }
}

// عرض تقرير الأرصدة للفترة المحددة
function displayBalancePeriodReport(data) {
    currentReportData = data;
    currentReportType = 'balance-period';

    const fromDate = formatDate(data.period.from_date);
    const toDate = formatDate(data.period.to_date);

    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-balance-scale me-2 text-primary"></i>
                    تقرير الأرصدة للفترة من ${fromDate} إلى ${toDate}
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="printEnhancedReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportToPDF('balance-period')">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card bg-danger text-white">
                            <div class="stat-icon">
                                <i class="fas fa-minus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_debts)}</h3>
                                <p class="stat-label">إجمالي الديون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card bg-success text-white">
                            <div class="stat-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_remittances)}</h3>
                                <p class="stat-label">إجمالي الحوالات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card ${data.totals.total_balance >= 0 ? 'bg-warning' : 'bg-info'} text-white">
                            <div class="stat-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_balance)}</h3>
                                <p class="stat-label">صافي الرصيد</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card bg-primary text-white">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${data.totals.sponsors_count}</h3>
                                <p class="stat-label">عدد الرعاة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات التصنيف -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    الرعاة المدينون
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <h3 class="text-danger">${data.totals.debtors_count}</h3>
                                <p class="text-muted">راعي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-check-circle me-2"></i>
                                    الرعاة الدائنون
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <h3 class="text-success">${data.totals.creditors_count}</h3>
                                <p class="text-muted">راعي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-secondary">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-equals me-2"></i>
                                    الرعاة المتوازنون
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <h3 class="text-secondary">${data.totals.balanced_count}</h3>
                                <p class="text-muted">راعي</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني -->
                <div class="row mb-4 no-print">
                    <div class="col-12">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">توزيع الأرصدة حسب الراعي</h6>
                            <div class="chart-wrapper">
                                <canvas id="balanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الأرصدة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    تفاصيل الأرصدة لجميع الرعاة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>الراعي</th>
                                                <th>الهاتف</th>
                                                <th>الديون</th>
                                                <th>الحوالات</th>
                                                <th>الرصيد المتبقي</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
    `;

    // عرض تفاصيل الأرصدة
    data.sponsors_balance.forEach(sponsor => {
        const balanceClass = sponsor.period_balance > 0 ? 'text-danger' :
                           sponsor.period_balance < 0 ? 'text-success' : 'text-muted';
        const statusBadge = sponsor.status === 'مدين' ? 'badge bg-danger' :
                           sponsor.status === 'دائن' ? 'badge bg-success' : 'badge bg-secondary';

        html += `
            <tr>
                <td><strong>${sponsor.sponsor_name}</strong></td>
                <td>${sponsor.sponsor_phone || 'غير محدد'}</td>
                <td class="text-danger text-currency">${formatCurrency(sponsor.period_debts)}</td>
                <td class="text-success text-currency">${formatCurrency(sponsor.period_remittances)}</td>
                <td class="${balanceClass} text-currency"><strong>${formatCurrency(sponsor.period_balance)}</strong></td>
                <td><span class="${statusBadge}">${sponsor.status}</span></td>
            </tr>
        `;
    });

    html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('report-content').innerHTML = html;

    // إنشاء الرسم البياني
    setTimeout(() => {
        createBalanceChartForPeriod(data);
    }, 100);

    // إضافة event listeners للطباعة
    if (typeof window !== 'undefined') {
        // إزالة المستمعين السابقين لتجنب التكرار
        window.removeEventListener('beforeprint', handleBeforePrint);
        window.removeEventListener('afterprint', handleAfterPrint);
        
        // إضافة المستمعين الجدد
        window.addEventListener('beforeprint', handleBeforePrint);
        window.addEventListener('afterprint', handleAfterPrint);
    }
}

// متغير لحفظ مرجع الرسم البياني
let balanceChart = null;

// إنشاء الرسم البياني لتقرير الأرصدة
function createBalanceChartForPeriod(data) {
    const ctx = document.getElementById('balanceChart');
    if (!ctx) return;

    // تدمير الرسم السابق إن وجد
    if (balanceChart) {
        balanceChart.destroy();
        balanceChart = null;
    }

    setupCanvasSize(ctx, 'balance');

    const labels = data.sponsors_balance.map(s => s.sponsor_name);
    const debtsData = data.sponsors_balance.map(s => s.period_debts);
    const remittancesData = data.sponsors_balance.map(s => s.period_remittances);
    const balanceData = data.sponsors_balance.map(s => s.period_balance);

    balanceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'الديون',
                    data: debtsData,
                    backgroundColor: 'rgba(220, 53, 69, 0.8)',
                    borderColor: '#dc3545',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: '#dc3545',
                    hoverBorderWidth: 3
                },
                {
                    label: 'الحوالات',
                    data: remittancesData,
                    backgroundColor: 'rgba(25, 135, 84, 0.8)',
                    borderColor: '#198754',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: '#198754',
                    hoverBorderWidth: 3
                },
                {
                    label: 'الرصيد المتبقي',
                    data: balanceData,
                    backgroundColor: balanceData.map(value =>
                        value >= 0 ? 'rgba(255, 193, 7, 0.8)' : 'rgba(13, 110, 253, 0.8)'
                    ),
                    borderColor: balanceData.map(value =>
                        value >= 0 ? '#ffc107' : '#0d6efd'
                    ),
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: balanceData.map(value =>
                        value >= 0 ? '#ffc107' : '#0d6efd'
                    ),
                    hoverBorderWidth: 3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 2.5,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 12,
                            weight: '600'
                        },
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'rect'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#0d6efd',
                    borderWidth: 2,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    },
                    titleFont: { family: 'Noto Sans Arabic', weight: 'bold' },
                    bodyFont: { family: 'Noto Sans Arabic' }
                }
            },
            scales: {
                x: {
                    ticks: {
                        maxRotation: 45,
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 11,
                            weight: '600'
                        },
                        color: '#495057'
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        },
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 11
                        },
                        color: '#495057'
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        borderDash: [5, 5]
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// معالجة ما قبل الطباعة
function handleBeforePrint() {
    if (currentReportData && currentReportType === 'balance-period') {
        window.savedReportData = currentReportData;
    }
}

// معالجة ما بعد الطباعة - إعادة إنشاء الرسم البياني
function handleAfterPrint() {
    if (window.savedReportData && currentReportType === 'balance-period') {
        setTimeout(() => {
            createBalanceChartForPeriod(window.savedReportData);
        }, 200);
    }
}
