// نظام المحاسبة للرعاة - الرسوم البيانية
// Charts functionality for Accounting System

// متغيرات الرسوم البيانية
let summaryChart = null;
let sponsorsChart = null;
let trendsChart = null;

// إعداد أبعاد Canvas بشكل صحيح
function setupCanvasSize(canvas, chartType) {
    const container = canvas.closest('.chart-container');
    if (!container) return;

    const wrapper = canvas.closest('.chart-wrapper');
    if (!wrapper) return;

    // إزالة أي أبعاد مباشرة من Canvas
    canvas.removeAttribute('width');
    canvas.removeAttribute('height');
    canvas.style.width = '';
    canvas.style.height = '';

    // ضبط أبعاد الحاوية حسب نوع الرسم
    switch(chartType) {
        case 'summary':
            wrapper.style.height = '300px';
            break;
        case 'sponsors':
            wrapper.style.height = '350px';
            break;
        case 'trends':
            wrapper.style.height = '400px';
            break;
        default:
            wrapper.style.height = '300px';
    }
}

// إعدادات الألوان المحسنة
const chartColors = {
    primary: '#0d6efd',
    success: '#198754',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#0dcaf0',
    light: '#f8f9fa',
    dark: '#212529',
    gradient: {
        primary: ['#0d6efd', '#6610f2'],
        success: ['#198754', '#20c997'],
        danger: ['#dc3545', '#fd7e14'],
        warning: ['#ffc107', '#fd7e14'],
        info: ['#0dcaf0', '#6f42c1']
    },
    // مجموعة ألوان متدرجة للرسوم البيانية
    palette: [
        '#198754', '#20c997', '#0dcaf0', '#6610f2',
        '#fd7e14', '#ffc107', '#e83e8c', '#6c757d',
        '#17a2b8', '#28a745', '#007bff', '#6f42c1'
    ],
    // ألوان خاصة للتقارير
    reports: {
        remittances: {
            primary: '#198754',
            secondary: '#20c997',
            gradient: 'linear-gradient(135deg, #198754 0%, #20c997 100%)',
            shadow: 'rgba(25, 135, 84, 0.3)'
        },
        debts: {
            primary: '#dc3545',
            secondary: '#fd7e14',
            gradient: 'linear-gradient(135deg, #dc3545 0%, #fd7e14 100%)',
            shadow: 'rgba(220, 53, 69, 0.3)'
        },
        balance: {
            primary: '#0d6efd',
            secondary: '#6610f2',
            gradient: 'linear-gradient(135deg, #0d6efd 0%, #6610f2 100%)',
            shadow: 'rgba(13, 110, 253, 0.3)'
        }
    }
};

// إنشاء رسم بياني دائري للملخص الإجمالي
function createSummaryChart(data) {
    const ctx = document.getElementById('summaryChart');
    if (!ctx) return;

    // تدمير الرسم السابق إن وجد
    if (summaryChart) {
        summaryChart.destroy();
    }

    // ضبط أبعاد Canvas
    setupCanvasSize(ctx, 'summary');

    const totalDebts = data.totals.total_debts;
    const totalRemittances = data.totals.total_remittances;
    const totalBalance = data.totals.total_balance;

    summaryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['إجمالي الديون', 'إجمالي الحوالات', 'الرصيد المتبقي'],
            datasets: [{
                data: [totalDebts, totalRemittances, Math.abs(totalBalance)],
                backgroundColor: [
                    chartColors.danger,
                    chartColors.success,
                    totalBalance >= 0 ? chartColors.warning : chartColors.info
                ],
                borderWidth: 3,
                borderColor: '#fff',
                hoverBorderWidth: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 1.5, // نسبة العرض إلى الارتفاع
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${context.label}: ${formatCurrency(value)} (${percentage}%)`;
                        }
                    },
                    titleFont: {
                        family: 'Noto Sans Arabic'
                    },
                    bodyFont: {
                        family: 'Noto Sans Arabic'
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 1500
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                }
            }
        }
    });
}

// إنشاء رسم بياني عمودي للرعاة
function createSponsorsChart(data) {
    const ctx = document.getElementById('sponsorsChart');
    if (!ctx) return;

    // تدمير الرسم السابق إن وجد
    if (sponsorsChart) {
        sponsorsChart.destroy();
    }

    // ضبط أبعاد Canvas
    setupCanvasSize(ctx, 'sponsors');

    // ترتيب الرعاة حسب الرصيد المتبقي
    const sortedSponsors = data.sponsors.sort((a, b) => b.balance - a.balance).slice(0, 10);
    
    const labels = sortedSponsors.map(sponsor => sponsor.name);
    const debtsData = sortedSponsors.map(sponsor => sponsor.total_debts);
    const remittancesData = sortedSponsors.map(sponsor => sponsor.total_remittances);
    const balanceData = sortedSponsors.map(sponsor => sponsor.balance);

    sponsorsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'الديون',
                    data: debtsData,
                    backgroundColor: chartColors.danger,
                    borderColor: chartColors.danger,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                },
                {
                    label: 'الحوالات',
                    data: remittancesData,
                    backgroundColor: chartColors.success,
                    borderColor: chartColors.success,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                },
                {
                    label: 'الرصيد المتبقي',
                    data: balanceData,
                    backgroundColor: chartColors.warning,
                    borderColor: chartColors.warning,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 2, // نسبة العرض إلى الارتفاع للرسم العمودي
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    },
                    titleFont: {
                        family: 'Noto Sans Arabic'
                    },
                    bodyFont: {
                        family: 'Noto Sans Arabic'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 10
                        },
                        maxRotation: 45
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        },
                        font: {
                            family: 'Noto Sans Arabic'
                        }
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                }
            }
        }
    });
}

// إنشاء رسم بياني خطي للاتجاهات الزمنية
function createTrendsChart(debtsData, remittancesData) {
    const ctx = document.getElementById('trendsChart');
    if (!ctx) return;

    // تدمير الرسم السابق إن وجد
    if (trendsChart) {
        trendsChart.destroy();
    }

    // ضبط أبعاد Canvas
    setupCanvasSize(ctx, 'trends');

    // تجميع البيانات حسب التاريخ
    const dateGroups = {};
    
    // معالجة الديون
    debtsData.forEach(debt => {
        const date = debt.date;
        if (!dateGroups[date]) {
            dateGroups[date] = { debts: 0, remittances: 0 };
        }
        dateGroups[date].debts += debt.amount;
    });

    // معالجة الحوالات
    remittancesData.forEach(remittance => {
        const date = remittance.date;
        if (!dateGroups[date]) {
            dateGroups[date] = { debts: 0, remittances: 0 };
        }
        dateGroups[date].remittances += remittance.amount;
    });

    // ترتيب التواريخ وإعداد البيانات
    const sortedDates = Object.keys(dateGroups).sort();
    const labels = sortedDates.map(date => formatDate(date));
    const debtsChartData = sortedDates.map(date => dateGroups[date].debts);
    const remittancesChartData = sortedDates.map(date => dateGroups[date].remittances);

    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'الديون اليومية',
                    data: debtsChartData,
                    borderColor: chartColors.danger,
                    backgroundColor: chartColors.danger + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: chartColors.danger,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                },
                {
                    label: 'الحوالات اليومية',
                    data: remittancesChartData,
                    borderColor: chartColors.success,
                    backgroundColor: chartColors.success + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: chartColors.success,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 2.5, // نسبة العرض إلى الارتفاع للرسم الخطي
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                        }
                    },
                    titleFont: {
                        family: 'Noto Sans Arabic'
                    },
                    bodyFont: {
                        family: 'Noto Sans Arabic'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        font: {
                            family: 'Noto Sans Arabic',
                            size: 10
                        },
                        maxRotation: 45
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        },
                        font: {
                            family: 'Noto Sans Arabic'
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                }
            }
        }
    });
}

// تدمير جميع الرسوم البيانية
function destroyAllCharts() {
    if (summaryChart) {
        summaryChart.destroy();
        summaryChart = null;
    }
    if (sponsorsChart) {
        sponsorsChart.destroy();
        sponsorsChart = null;
    }
    if (trendsChart) {
        trendsChart.destroy();
        trendsChart = null;
    }
}

// إعادة تحجيم الرسوم البيانية عند تغيير حجم النافذة
function resizeCharts() {
    if (summaryChart) {
        summaryChart.resize();
    }
    if (sponsorsChart) {
        sponsorsChart.resize();
    }
    if (trendsChart) {
        trendsChart.resize();
    }
}

// مراقبة تغيير حجم النافذة
let resizeTimeout;
window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        resizeCharts();
    }, 250);
});

// التأكد من تحميل Chart.js بشكل صحيح
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود Chart.js
    if (typeof Chart === 'undefined') {
        console.error('Chart.js غير محمل بشكل صحيح');
        return;
    }

    // إعداد Chart.js العام
    Chart.defaults.font.family = 'Noto Sans Arabic, sans-serif';
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#495057';

    console.log('✅ تم تحميل Chart.js بنجاح');
});
