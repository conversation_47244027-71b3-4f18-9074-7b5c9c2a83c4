from decimal import Decimal
from django.db.models import Sum
from rest_framework import serializers
from .models import Seller, WorkDay, DailyEntry, DayRates, DailyCalculation, FinalSale


class SellerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Seller
        fields = ['id', 'name', 'phone', 'is_active', 'created_at', 'updated_at']


class WorkDaySerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkDay
        fields = ['id', 'work_date', 'notes']


class DailyEntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = DailyEntry
        fields = ['id', 'work_day', 'seller', 'amount_old', 'kilos', 'note', 'created_at']


class DayRatesSerializer(serializers.ModelSerializer):
    class Meta:
        model = DayRates
        fields = ['id', 'work_day', 'usd_to_old', 'usd_to_new', 'kilo_price', 'locked', 'created_at', 'updated_at']


class DailyCalculationSerializer(serializers.ModelSerializer):
    seller_name = serializers.CharField(source='seller.name', read_only=True)
    class Meta:
        model = DailyCalculation
        fields = [
            'id', 'work_day', 'seller', 'seller_name', 'amount_old_sum', 'kilos_sum',
            'usd_to_old', 'usd_to_new', 'kilo_price',
            'amount_new', 'cargo_amount', 'total_after_cargo', 'approved_at'
        ]


class FinalSaleSerializer(serializers.ModelSerializer):
    seller_name = serializers.CharField(source='seller.name', read_only=True)
    class Meta:
        model = FinalSale
        fields = [
            'id', 'work_day', 'seller', 'seller_name', 'calc_total', 'actual_sale', 'profit_loss', 'created_at'
        ]


class DailyEntryBulkSerializer(serializers.Serializer):
    work_day = serializers.PrimaryKeyRelatedField(queryset=WorkDay.objects.all())
    entries = serializers.ListField(child=serializers.DictField(), allow_empty=False)

    def validate(self, attrs):
        # Basic validations
        for e in attrs['entries']:
            if 'seller' not in e or 'amount_old' not in e or 'kilos' not in e:
                raise serializers.ValidationError('Each entry must include seller, amount_old, kilos')
        return attrs

    def create(self, validated_data):
        work_day = validated_data['work_day']
        entries = []
        for e in validated_data['entries']:
            entries.append(DailyEntry(
                work_day=work_day,
                seller_id=e['seller'],
                amount_old=e['amount_old'],
                kilos=e['kilos'],
                note=e.get('note')
            ))
        DailyEntry.objects.bulk_create(entries)
        return entries