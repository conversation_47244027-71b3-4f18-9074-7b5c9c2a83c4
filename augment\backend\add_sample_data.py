# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لنظام المحاسبة
Add Sample Data for Accounting System
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, Sponsor, Debt, Remittance, Branch

def add_sample_data():
    """إضافة بيانات تجريبية"""
    with app.app_context():
        print("🔄 إضافة بيانات تجريبية...")
        
        # حذف البيانات الموجودة
        db.session.query(Remittance).delete()
        db.session.query(Debt).delete()
        db.session.query(Sponsor).delete()
        db.session.query(Branch).delete()
        db.session.commit()
        
        # إضافة الفروع أولاً
        branches_data = [
            {
                'name': 'فرع الغيظة',
                'code': 'GH',
                'description': 'فرع الغيظة الرئيسي',
                'address': 'الغيظة - شارع الرئيسي',
                'phone': '777111222',
                'manager_name': 'أحمد محمد الغيظي'
            },
            {
                'name': 'فرع شحن',
                'code': 'SH',
                'description': 'فرع شحن للخدمات اللوجستية',
                'address': 'شحن - المنطقة الصناعية',
                'phone': '777333444',
                'manager_name': 'علي سالم الشحني'
            }
        ]
        
        branches = []
        for branch_data in branches_data:
            branch = Branch(**branch_data)
            db.session.add(branch)
            branches.append(branch)
        
        db.session.commit()
        print(f"✅ تم إضافة {len(branches)} فرع")
        
        # إضافة رعاة تجريبيين
        sponsors_data = [
            {
                'name': 'أحمد محمد علي',
                'phone': '777123456',
                'address': 'صنعاء - شارع الزبيري'
            },
            {
                'name': 'فاطمة عبدالله',
                'phone': '773987654',
                'address': 'عدن - كريتر'
            },
            {
                'name': 'محمد سالم',
                'phone': '770555777',
                'address': 'تعز - وسط المدينة'
            },
            {
                'name': 'عائشة حسن',
                'phone': '771222333',
                'address': 'الحديدة - الميناء'
            },
            {
                'name': 'علي عبدالرحمن',
                'phone': '774444555',
                'address': 'إب - السوق'
            }
        ]
        
        sponsors = []
        for sponsor_data in sponsors_data:
            sponsor = Sponsor(**sponsor_data)
            db.session.add(sponsor)
            sponsors.append(sponsor)
        
        db.session.commit()
        print(f"✅ تم إضافة {len(sponsors)} راعي")
        
        # إضافة ديون تجريبية موزعة على الفروع
        debts_data = []
        base_date = date.today() - timedelta(days=30)
        
        for i, sponsor in enumerate(sponsors):
            # إضافة عدة ديون لكل راعي موزعة على الفروع
            for j in range(3, 8):  # من 3 إلى 7 ديون لكل راعي
                debt_date = base_date + timedelta(days=j + i*3)
                amount = (j + 1) * 50000 + (i * 25000)  # مبالغ متنوعة
                
                # توزيع الديون على الفروع (70% فرع الغيظة، 30% فرع شحن)
                branch = branches[0] if (i + j) % 10 < 7 else branches[1]
                
                debt = Debt(
                    sponsor_id=sponsor.id,
                    branch_id=branch.id,
                    amount=amount,
                    description=f'دين رقم {j+1} - {sponsor.name} - {branch.name}',
                    date=debt_date
                )
                debts_data.append(debt)
                db.session.add(debt)
        
        db.session.commit()
        print(f"✅ تم إضافة {len(debts_data)} دين")
        
        # إضافة حوالات تجريبية
        remittances_data = []
        
        for i, sponsor in enumerate(sponsors):
            # إضافة عدة حوالات لكل راعي
            for j in range(2, 5):  # من 2 إلى 4 حوالات لكل راعي
                remittance_date = base_date + timedelta(days=j*2 + i*4)
                amount = (j + 1) * 30000 + (i * 15000)  # مبالغ متنوعة
                
                sources = ['بنك الكريمي', 'بنك سبأ', 'بنك اليمن الدولي', 'حوالة نقدية', 'تحويل بنكي']
                source = sources[j % len(sources)]
                
                remittance = Remittance(
                    sponsor_id=sponsor.id,
                    amount=amount,
                    source=source,
                    date=remittance_date
                )
                remittances_data.append(remittance)
                db.session.add(remittance)
        
        db.session.commit()
        print(f"✅ تم إضافة {len(remittances_data)} حوالة")
        
        # طباعة ملخص البيانات
        print("\n📊 ملخص البيانات المضافة:")
        print("=" * 50)
        
        # ملخص الفروع
        print("\n🏢 الفروع:")
        for branch in branches:
            total_debts = branch.get_total_debts()
            debts_count = branch.get_debts_count()
            print(f"🏪 {branch.name} ({branch.code})")
            print(f"   👨‍💼 المدير: {branch.manager_name}")
            print(f"   📞 الهاتف: {branch.phone}")
            print(f"   💰 إجمالي الديون: {total_debts:,.0f} ريال")
            print(f"   📊 عدد الديون: {debts_count}")
            print("-" * 30)
        
        # ملخص الرعاة
        print("\n👥 الرعاة:")
        for sponsor in sponsors:
            total_debts = sponsor.get_total_debts()
            total_remittances = sponsor.get_total_remittances()
            balance = sponsor.get_balance()
            
            print(f"👤 {sponsor.name}")
            print(f"   📞 {sponsor.phone}")
            print(f"   💰 إجمالي الديون: {total_debts:,.0f} ريال")
            print(f"   💸 إجمالي الحوالات: {total_remittances:,.0f} ريال")
            print(f"   📈 الرصيد المتبقي: {balance:,.0f} ريال")
            print("-" * 30)
        
        # إحصائيات عامة
        total_sponsors = len(sponsors)
        total_branches = len(branches)
        total_debts_amount = sum(sponsor.get_total_debts() for sponsor in sponsors)
        total_remittances_amount = sum(sponsor.get_total_remittances() for sponsor in sponsors)
        total_balance = total_debts_amount - total_remittances_amount
        
        print(f"\n🏆 الإحصائيات العامة:")
        print(f"   🏢 عدد الفروع: {total_branches}")
        print(f"   👥 عدد الرعاة: {total_sponsors}")
        print(f"   📋 عدد الديون: {len(debts_data)}")
        print(f"   📋 عدد الحوالات: {len(remittances_data)}")
        print(f"   💰 إجمالي الديون: {total_debts_amount:,.0f} ريال")
        print(f"   💸 إجمالي الحوالات: {total_remittances_amount:,.0f} ريال")
        print(f"   📊 الرصيد الإجمالي: {total_balance:,.0f} ريال")
        
        # إحصائيات الفروع
        print(f"\n📈 توزيع الديون على الفروع:")
        for branch in branches:
            branch_total = branch.get_total_debts()
            branch_count = branch.get_debts_count()
            percentage = (branch_total / total_debts_amount * 100) if total_debts_amount > 0 else 0
            print(f"   🏪 {branch.name}: {branch_total:,.0f} ريال ({percentage:.1f}%) - {branch_count} دين")
        
        print("\n🎉 تم إضافة البيانات التجريبية بنجاح!")
        print("🌐 يمكنك الآن زيارة: http://localhost:5000")
        print("\n📝 ملاحظات هامة:")
        print("   • تم إنشاء فرعين: فرع الغيظة وفرع شحن")
        print("   • تم توزيع الديون على الفروع (70% الغيظة، 30% شحن)")
        print("   • يمكن الآن استخدام نظام التقارير المتقدم للفروع")
        print("   • جميع API endpoints تدعم الآن الفروع والفلترة")

if __name__ == '__main__':
    add_sample_data()
