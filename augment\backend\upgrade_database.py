# -*- coding: utf-8 -*-
"""
سكريبت ترقية قاعدة البيانات - إضافة حقل الاسم الكامل للرعاة
Database Upgrade Script - Add full_name field to sponsors
"""

import sqlite3
import os
from datetime import datetime

def upgrade_database():
    """ترقية قاعدة البيانات لإضافة حقل الاسم الكامل"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), '..', 'database', 'accounting.db')
    
    print("🔄 بدء ترقية قاعدة البيانات...")
    print(f"📁 مسار قاعدة البيانات: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود العمود full_name
        cursor.execute("PRAGMA table_info(sponsors)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'full_name' in columns:
            print("✅ حقل full_name موجود مسبقاً")
            return True
        
        print("📝 إضافة حقل full_name إلى جدول الرعاة...")
        
        # إضافة العمود الجديد
        cursor.execute("ALTER TABLE sponsors ADD COLUMN full_name VARCHAR(200)")
        
        # تحديث البيانات الحالية - نسخ الاسم الحالي كاسم كامل مؤقت
        cursor.execute("UPDATE sponsors SET full_name = name WHERE full_name IS NULL")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من نجاح العملية
        cursor.execute("SELECT COUNT(*) FROM sponsors WHERE full_name IS NOT NULL")
        updated_count = cursor.fetchone()[0]
        
        print(f"✅ تم تحديث {updated_count} راعي بنجاح")
        print("✅ تمت ترقية قاعدة البيانات بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترقية قاعدة البيانات: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

def verify_upgrade():
    """التحقق من نجاح الترقية"""
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'database', 'accounting.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص هيكل الجدول
        cursor.execute("PRAGMA table_info(sponsors)")
        columns = cursor.fetchall()
        
        print("\n📋 هيكل جدول الرعاة بعد الترقية:")
        for column in columns:
            print(f"   - {column[1]} ({column[2]})")
        
        # عرض عينة من البيانات
        cursor.execute("SELECT id, name, full_name FROM sponsors LIMIT 5")
        sponsors = cursor.fetchall()
        
        print("\n👥 عينة من بيانات الرعاة:")
        for sponsor in sponsors:
            print(f"   - ID: {sponsor[0]}, الاسم الأول: {sponsor[1]}, الاسم الكامل: {sponsor[2]}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("=" * 50)
    print("🔧 سكريبت ترقية قاعدة البيانات")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية إضافية
    db_path = os.path.join(os.path.dirname(__file__), '..', 'database', 'accounting.db')
    backup_path = os.path.join(os.path.dirname(__file__), '..', 'database', 
                              f'accounting_pre_upgrade_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
    except Exception as e:
        print(f"⚠️ تحذير: فشل في إنشاء نسخة احتياطية: {str(e)}")
    
    # تنفيذ الترقية
    if upgrade_database():
        verify_upgrade()
        print("\n🎉 تمت الترقية بنجاح! يمكنك الآن تشغيل التطبيق.")
    else:
        print("\n❌ فشلت الترقية. يرجى مراجعة الأخطاء أعلاه.")
